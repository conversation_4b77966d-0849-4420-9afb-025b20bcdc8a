"""
Knowledge Management for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
import json
from pathlib import Path
import hashlib
import re

from ..config import config

logger = logging.getLogger(__name__)


@dataclass
class KnowledgeItem:
    """Individual knowledge item"""
    item_id: str
    title: str
    content: str
    category: str
    tags: List[str] = field(default_factory=list)
    source: Optional[str] = None
    confidence: float = 1.0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchResult:
    """Search result representation"""
    item: KnowledgeItem
    relevance_score: float
    matched_fields: List[str]
    snippet: str


class KnowledgeManager:
    """Advanced knowledge management system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize knowledge manager"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.knowledge_file = self.data_dir / "knowledge_base.json"
        self.index_file = self.data_dir / "knowledge_index.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Storage
        self.knowledge_items: Dict[str, KnowledgeItem] = {}
        self.search_index: Dict[str, List[str]] = {}  # word -> item_ids
        self.category_index: Dict[str, List[str]] = {}  # category -> item_ids
        self.tag_index: Dict[str, List[str]] = {}  # tag -> item_ids
        
        # Load existing data
        self._load_knowledge_base()
        self._build_search_index()
        
        logger.info("Knowledge manager initialized")
    
    def add_knowledge(
        self,
        title: str,
        content: str,
        category: str,
        tags: Optional[List[str]] = None,
        source: Optional[str] = None,
        confidence: float = 1.0
    ) -> str:
        """Add new knowledge item"""
        # Generate unique ID
        item_id = hashlib.md5(f"{title}_{content}".encode()).hexdigest()
        
        # Check if item already exists
        if item_id in self.knowledge_items:
            # Update existing item
            existing_item = self.knowledge_items[item_id]
            existing_item.content = content
            existing_item.tags = tags or existing_item.tags
            existing_item.updated_at = datetime.now()
            existing_item.confidence = max(existing_item.confidence, confidence)
            
            logger.info(f"Updated knowledge item: {title}")
        else:
            # Create new item
            item = KnowledgeItem(
                item_id=item_id,
                title=title,
                content=content,
                category=category,
                tags=tags or [],
                source=source,
                confidence=confidence
            )
            
            self.knowledge_items[item_id] = item
            logger.info(f"Added knowledge item: {title}")
        
        # Update indices
        self._update_indices(item_id)
        
        # Save changes
        self._save_knowledge_base()
        
        return item_id
    
    def search_knowledge(
        self,
        query: str,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[SearchResult]:
        """Search knowledge base"""
        query_words = self._tokenize_text(query.lower())
        
        # Find candidate items
        candidate_items = set()
        
        # Search by words
        for word in query_words:
            if word in self.search_index:
                candidate_items.update(self.search_index[word])
        
        # Filter by category
        if category and category in self.category_index:
            category_items = set(self.category_index[category])
            candidate_items = candidate_items.intersection(category_items)
        
        # Filter by tags
        if tags:
            for tag in tags:
                if tag in self.tag_index:
                    tag_items = set(self.tag_index[tag])
                    candidate_items = candidate_items.intersection(tag_items)
        
        # Score and rank results
        results = []
        for item_id in candidate_items:
            if item_id in self.knowledge_items:
                item = self.knowledge_items[item_id]
                score, matched_fields = self._calculate_relevance_score(item, query_words)
                
                if score > 0:
                    snippet = self._generate_snippet(item.content, query_words)
                    result = SearchResult(
                        item=item,
                        relevance_score=score,
                        matched_fields=matched_fields,
                        snippet=snippet
                    )
                    results.append(result)
        
        # Sort by relevance score
        results.sort(key=lambda r: r.relevance_score, reverse=True)
        
        # Update access statistics
        for result in results[:limit]:
            result.item.access_count += 1
            result.item.last_accessed = datetime.now()
        
        return results[:limit]
    
    def get_knowledge_by_id(self, item_id: str) -> Optional[KnowledgeItem]:
        """Get knowledge item by ID"""
        if item_id in self.knowledge_items:
            item = self.knowledge_items[item_id]
            item.access_count += 1
            item.last_accessed = datetime.now()
            return item
        return None
    
    def get_knowledge_by_category(self, category: str) -> List[KnowledgeItem]:
        """Get all knowledge items in a category"""
        if category in self.category_index:
            item_ids = self.category_index[category]
            return [self.knowledge_items[item_id] for item_id in item_ids if item_id in self.knowledge_items]
        return []
    
    def get_knowledge_by_tag(self, tag: str) -> List[KnowledgeItem]:
        """Get all knowledge items with a specific tag"""
        if tag in self.tag_index:
            item_ids = self.tag_index[tag]
            return [self.knowledge_items[item_id] for item_id in item_ids if item_id in self.knowledge_items]
        return []
    
    def update_knowledge(
        self,
        item_id: str,
        title: Optional[str] = None,
        content: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        confidence: Optional[float] = None
    ) -> bool:
        """Update existing knowledge item"""
        if item_id not in self.knowledge_items:
            return False
        
        item = self.knowledge_items[item_id]
        
        # Update fields
        if title is not None:
            item.title = title
        if content is not None:
            item.content = content
        if category is not None:
            item.category = category
        if tags is not None:
            item.tags = tags
        if confidence is not None:
            item.confidence = confidence
        
        item.updated_at = datetime.now()
        
        # Update indices
        self._update_indices(item_id)
        
        # Save changes
        self._save_knowledge_base()
        
        logger.info(f"Updated knowledge item: {item.title}")
        return True
    
    def delete_knowledge(self, item_id: str) -> bool:
        """Delete knowledge item"""
        if item_id not in self.knowledge_items:
            return False
        
        item = self.knowledge_items[item_id]
        del self.knowledge_items[item_id]
        
        # Remove from indices
        self._remove_from_indices(item_id)
        
        # Save changes
        self._save_knowledge_base()
        
        logger.info(f"Deleted knowledge item: {item.title}")
        return True
    
    def get_categories(self) -> List[str]:
        """Get all categories"""
        return list(self.category_index.keys())
    
    def get_tags(self) -> List[str]:
        """Get all tags"""
        return list(self.tag_index.keys())
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get knowledge base statistics"""
        total_items = len(self.knowledge_items)
        categories = len(self.category_index)
        tags = len(self.tag_index)
        
        # Calculate average confidence
        avg_confidence = sum(item.confidence for item in self.knowledge_items.values()) / total_items if total_items > 0 else 0
        
        # Find most accessed items
        most_accessed = sorted(
            self.knowledge_items.values(),
            key=lambda item: item.access_count,
            reverse=True
        )[:5]
        
        # Category distribution
        category_distribution = {}
        for category, item_ids in self.category_index.items():
            category_distribution[category] = len(item_ids)
        
        return {
            "total_items": total_items,
            "categories": categories,
            "tags": tags,
            "average_confidence": round(avg_confidence, 2),
            "category_distribution": category_distribution,
            "most_accessed": [
                {
                    "title": item.title,
                    "access_count": item.access_count,
                    "category": item.category
                }
                for item in most_accessed
            ]
        }
    
    def export_knowledge(self, output_file: Path, format: str = "json") -> bool:
        """Export knowledge base"""
        try:
            if format.lower() == "json":
                export_data = {}
                for item_id, item in self.knowledge_items.items():
                    export_data[item_id] = {
                        "item_id": item.item_id,
                        "title": item.title,
                        "content": item.content,
                        "category": item.category,
                        "tags": item.tags,
                        "source": item.source,
                        "confidence": item.confidence,
                        "created_at": item.created_at.isoformat(),
                        "updated_at": item.updated_at.isoformat(),
                        "access_count": item.access_count,
                        "last_accessed": item.last_accessed.isoformat() if item.last_accessed else None,
                        "metadata": item.metadata
                    }
                
                with open(output_file, 'w') as f:
                    json.dump(export_data, f, indent=2)
                    
            elif format.lower() == "txt":
                with open(output_file, 'w') as f:
                    for item in self.knowledge_items.values():
                        f.write(f"Title: {item.title}\n")
                        f.write(f"Category: {item.category}\n")
                        f.write(f"Tags: {', '.join(item.tags)}\n")
                        f.write(f"Content: {item.content}\n")
                        f.write("-" * 80 + "\n\n")
            
            logger.info(f"Exported knowledge base to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting knowledge base: {e}")
            return False
    
    def import_knowledge(self, input_file: Path, format: str = "json") -> int:
        """Import knowledge base"""
        try:
            imported_count = 0
            
            if format.lower() == "json":
                with open(input_file, 'r') as f:
                    import_data = json.load(f)
                
                for item_data in import_data.values():
                    self.add_knowledge(
                        title=item_data["title"],
                        content=item_data["content"],
                        category=item_data["category"],
                        tags=item_data.get("tags", []),
                        source=item_data.get("source"),
                        confidence=item_data.get("confidence", 1.0)
                    )
                    imported_count += 1
            
            logger.info(f"Imported {imported_count} knowledge items")
            return imported_count
            
        except Exception as e:
            logger.error(f"Error importing knowledge base: {e}")
            return 0
    
    def _tokenize_text(self, text: str) -> List[str]:
        """Tokenize text into words"""
        # Simple tokenization - split on whitespace and punctuation
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
        }
        
        return [word for word in words if word not in stop_words and len(word) > 2]
    
    def _calculate_relevance_score(self, item: KnowledgeItem, query_words: List[str]) -> Tuple[float, List[str]]:
        """Calculate relevance score for an item"""
        score = 0.0
        matched_fields = []
        
        # Tokenize item text
        title_words = self._tokenize_text(item.title)
        content_words = self._tokenize_text(item.content)
        tag_words = [tag.lower() for tag in item.tags]
        
        # Score title matches (highest weight)
        title_matches = len(set(query_words).intersection(set(title_words)))
        if title_matches > 0:
            score += title_matches * 3.0
            matched_fields.append("title")
        
        # Score content matches
        content_matches = len(set(query_words).intersection(set(content_words)))
        if content_matches > 0:
            score += content_matches * 1.0
            matched_fields.append("content")
        
        # Score tag matches (medium weight)
        tag_matches = len(set(query_words).intersection(set(tag_words)))
        if tag_matches > 0:
            score += tag_matches * 2.0
            matched_fields.append("tags")
        
        # Apply confidence multiplier
        score *= item.confidence
        
        # Boost score for recently accessed items
        if item.last_accessed:
            days_since_access = (datetime.now() - item.last_accessed).days
            if days_since_access < 7:
                score *= 1.2
        
        return score, matched_fields
    
    def _generate_snippet(self, content: str, query_words: List[str], max_length: int = 200) -> str:
        """Generate snippet highlighting query words"""
        # Find the best sentence containing query words
        sentences = content.split('.')
        best_sentence = ""
        max_matches = 0
        
        for sentence in sentences:
            sentence_words = self._tokenize_text(sentence)
            matches = len(set(query_words).intersection(set(sentence_words)))
            if matches > max_matches:
                max_matches = matches
                best_sentence = sentence.strip()
        
        # Truncate if too long
        if len(best_sentence) > max_length:
            best_sentence = best_sentence[:max_length] + "..."
        
        return best_sentence or content[:max_length] + "..."
    
    def _build_search_index(self):
        """Build search index for fast lookups"""
        self.search_index.clear()
        self.category_index.clear()
        self.tag_index.clear()
        
        for item_id, item in self.knowledge_items.items():
            self._update_indices(item_id)
    
    def _update_indices(self, item_id: str):
        """Update search indices for an item"""
        if item_id not in self.knowledge_items:
            return
        
        item = self.knowledge_items[item_id]
        
        # Remove from existing indices
        self._remove_from_indices(item_id)
        
        # Add to word index
        all_words = (
            self._tokenize_text(item.title) +
            self._tokenize_text(item.content) +
            [tag.lower() for tag in item.tags]
        )
        
        for word in set(all_words):
            if word not in self.search_index:
                self.search_index[word] = []
            if item_id not in self.search_index[word]:
                self.search_index[word].append(item_id)
        
        # Add to category index
        if item.category not in self.category_index:
            self.category_index[item.category] = []
        if item_id not in self.category_index[item.category]:
            self.category_index[item.category].append(item_id)
        
        # Add to tag index
        for tag in item.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = []
            if item_id not in self.tag_index[tag]:
                self.tag_index[tag].append(item_id)
    
    def _remove_from_indices(self, item_id: str):
        """Remove item from all indices"""
        # Remove from word index
        for word, item_ids in self.search_index.items():
            if item_id in item_ids:
                item_ids.remove(item_id)
        
        # Remove from category index
        for category, item_ids in self.category_index.items():
            if item_id in item_ids:
                item_ids.remove(item_id)
        
        # Remove from tag index
        for tag, item_ids in self.tag_index.items():
            if item_id in item_ids:
                item_ids.remove(item_id)
    
    def _load_knowledge_base(self):
        """Load knowledge base from file"""
        try:
            if self.knowledge_file.exists():
                with open(self.knowledge_file, 'r') as f:
                    knowledge_data = json.load(f)
                
                for item_id, item_dict in knowledge_data.items():
                    # Convert datetime strings back to datetime objects
                    item_dict["created_at"] = datetime.fromisoformat(item_dict["created_at"])
                    item_dict["updated_at"] = datetime.fromisoformat(item_dict["updated_at"])
                    if item_dict.get("last_accessed"):
                        item_dict["last_accessed"] = datetime.fromisoformat(item_dict["last_accessed"])
                    
                    item = KnowledgeItem(**item_dict)
                    self.knowledge_items[item_id] = item
                
                logger.info(f"Loaded {len(self.knowledge_items)} knowledge items")
                
        except Exception as e:
            logger.error(f"Error loading knowledge base: {e}")
    
    def _save_knowledge_base(self):
        """Save knowledge base to file"""
        try:
            knowledge_data = {}
            
            for item_id, item in self.knowledge_items.items():
                item_dict = {
                    "item_id": item.item_id,
                    "title": item.title,
                    "content": item.content,
                    "category": item.category,
                    "tags": item.tags,
                    "source": item.source,
                    "confidence": item.confidence,
                    "created_at": item.created_at.isoformat(),
                    "updated_at": item.updated_at.isoformat(),
                    "access_count": item.access_count,
                    "last_accessed": item.last_accessed.isoformat() if item.last_accessed else None,
                    "metadata": item.metadata
                }
                knowledge_data[item_id] = item_dict
            
            with open(self.knowledge_file, 'w') as f:
                json.dump(knowledge_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")
