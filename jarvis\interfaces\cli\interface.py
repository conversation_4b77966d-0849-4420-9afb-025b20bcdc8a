"""
Command Line Interface for JARVIS AI Assistant
"""

import asyncio
import sys
from typing import Optional
import logging

from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.text import Text
from rich.live import Live
from rich.spinner import Spinner

from ...core.ai import AIEngine
from ...core.memory import MemoryManager
from ...config import config

logger = logging.getLogger(__name__)


class CLIInterface:
    """Command Line Interface for JARVIS"""
    
    def __init__(self):
        """Initialize the CLI interface"""
        self.console = Console()
        self.ai_engine = AIEngine()
        self.memory_manager = MemoryManager()
        self.running = False
        
        # CLI configuration
        self.prompt_text = config.get("interface.cli.prompt", "JARVIS> ")
        self.use_colors = config.get("interface.cli.colors", True)
        
        logger.info("CLI interface initialized")
    
    def display_welcome(self):
        """Display welcome message"""
        welcome_text = Text()
        welcome_text.append("🤖 JARVIS AI Assistant\n", style="bold blue")
        welcome_text.append("Advanced AI Assistant with Voice Control and System Automation\n\n", style="dim")
        welcome_text.append("Commands:\n", style="bold")
        welcome_text.append("  /help     - Show help\n", style="cyan")
        welcome_text.append("  /status   - Show system status\n", style="cyan")
        welcome_text.append("  /memory   - Show memory stats\n", style="cyan")
        welcome_text.append("  /clear    - Clear conversation\n", style="cyan")
        welcome_text.append("  /config   - Show configuration\n", style="cyan")
        welcome_text.append("  /exit     - Exit JARVIS\n", style="cyan")
        welcome_text.append("\nType your message or command to get started!", style="green")
        
        panel = Panel(
            welcome_text,
            title="Welcome to JARVIS",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    def display_error(self, error: str):
        """Display error message"""
        self.console.print(f"❌ Error: {error}", style="bold red")
    
    def display_info(self, message: str):
        """Display info message"""
        self.console.print(f"ℹ️  {message}", style="blue")
    
    def display_success(self, message: str):
        """Display success message"""
        self.console.print(f"✅ {message}", style="green")
    
    async def process_command(self, user_input: str) -> bool:
        """
        Process user command
        
        Args:
            user_input: User's input
            
        Returns:
            True to continue, False to exit
        """
        user_input = user_input.strip()
        
        if not user_input:
            return True
        
        # Handle special commands
        if user_input.startswith('/'):
            return await self._handle_special_command(user_input)
        
        # Process regular message with AI
        try:
            # Show thinking indicator
            with Live(Spinner("dots", text="JARVIS is thinking..."), console=self.console):
                response = await self.ai_engine.process_message(user_input)
            
            # Display response
            self.console.print("\n🤖 JARVIS:", style="bold blue")
            self.console.print(response, style="white")
            self.console.print()
            
            # Store in memory
            self.memory_manager.store_conversation(user_input, response)
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            self.display_error(f"Failed to process message: {str(e)}")
            return True
    
    async def _handle_special_command(self, command: str) -> bool:
        """Handle special CLI commands"""
        command = command.lower()
        
        if command == "/exit" or command == "/quit":
            self.display_info("Goodbye! 👋")
            return False
        
        elif command == "/help":
            self._show_help()
        
        elif command == "/status":
            await self._show_status()
        
        elif command == "/memory":
            self._show_memory_stats()
        
        elif command == "/clear":
            self._clear_conversation()
        
        elif command == "/config":
            self._show_config()
        
        else:
            self.display_error(f"Unknown command: {command}")
            self.display_info("Type /help for available commands")
        
        return True
    
    def _show_help(self):
        """Show help information"""
        help_text = Text()
        help_text.append("JARVIS CLI Commands:\n\n", style="bold")
        help_text.append("/help     - Show this help message\n", style="cyan")
        help_text.append("/status   - Show AI engine and system status\n", style="cyan")
        help_text.append("/memory   - Show memory usage statistics\n", style="cyan")
        help_text.append("/clear    - Clear conversation history\n", style="cyan")
        help_text.append("/config   - Show current configuration\n", style="cyan")
        help_text.append("/exit     - Exit JARVIS\n\n", style="cyan")
        help_text.append("You can also type any message to chat with JARVIS!", style="green")
        
        panel = Panel(help_text, title="Help", border_style="cyan")
        self.console.print(panel)
    
    async def _show_status(self):
        """Show system status"""
        try:
            ai_info = self.ai_engine.get_provider_info()
            conversation_summary = self.ai_engine.get_conversation_summary()
            
            status_text = Text()
            status_text.append("🤖 AI Engine Status:\n", style="bold blue")
            status_text.append(f"  Provider: {ai_info.get('provider', 'Unknown')}\n", style="white")
            status_text.append(f"  Status: {ai_info.get('status', 'Unknown')}\n", style="white")
            status_text.append(f"  Model: {ai_info.get('model', 'Unknown')}\n", style="white")
            status_text.append(f"\n💬 Conversation: {conversation_summary}\n", style="bold green")
            
            panel = Panel(status_text, title="System Status", border_style="green")
            self.console.print(panel)
            
        except Exception as e:
            self.display_error(f"Failed to get status: {str(e)}")
    
    def _show_memory_stats(self):
        """Show memory statistics"""
        try:
            stats = self.memory_manager.get_memory_stats()
            
            memory_text = Text()
            memory_text.append("🧠 Memory Statistics:\n\n", style="bold purple")
            
            # Conversation stats
            conv_stats = stats.get("conversations", {})
            memory_text.append("💬 Conversations:\n", style="bold blue")
            memory_text.append(f"  Count: {conv_stats.get('count', 0)}\n", style="white")
            memory_text.append(f"  Size: {conv_stats.get('size_mb', 0)} MB\n", style="white")
            
            # Personal info stats
            personal_stats = stats.get("personal_info", {})
            memory_text.append("\n👤 Personal Information:\n", style="bold blue")
            memory_text.append(f"  Items: {personal_stats.get('items', 0)}\n", style="white")
            memory_text.append(f"  Size: {personal_stats.get('size_mb', 0)} MB\n", style="white")
            
            memory_text.append(f"\n📁 Total Size: {stats.get('total_size_mb', 0)} MB", style="bold yellow")
            
            panel = Panel(memory_text, title="Memory Statistics", border_style="purple")
            self.console.print(panel)
            
        except Exception as e:
            self.display_error(f"Failed to get memory stats: {str(e)}")
    
    def _clear_conversation(self):
        """Clear conversation history"""
        try:
            self.ai_engine.clear_conversation()
            self.memory_manager.clear_conversation_history()
            self.display_success("Conversation history cleared")
        except Exception as e:
            self.display_error(f"Failed to clear conversation: {str(e)}")
    
    def _show_config(self):
        """Show current configuration"""
        config_text = Text()
        config_text.append("⚙️  Current Configuration:\n\n", style="bold yellow")
        config_text.append(f"AI Provider: {config.get('ai.provider', 'Unknown')}\n", style="white")
        config_text.append(f"AI Model: {config.get('ai.openai.model', 'Unknown')}\n", style="white")
        config_text.append(f"Voice Recognition: {config.get('voice.recognition.provider', 'Unknown')}\n", style="white")
        config_text.append(f"Voice Synthesis: {config.get('voice.synthesis.provider', 'Unknown')}\n", style="white")
        config_text.append(f"Safe Mode: {config.get('system.file_operations.safe_mode', True)}\n", style="white")
        
        panel = Panel(config_text, title="Configuration", border_style="yellow")
        self.console.print(panel)
    
    async def run(self):
        """Run the CLI interface"""
        self.running = True
        self.display_welcome()
        
        try:
            while self.running:
                try:
                    # Get user input
                    user_input = Prompt.ask(
                        f"\n[bold blue]{self.prompt_text}[/bold blue]",
                        console=self.console
                    )
                    
                    # Process the input
                    should_continue = await self.process_command(user_input)
                    if not should_continue:
                        break
                        
                except KeyboardInterrupt:
                    self.console.print("\n\nReceived interrupt signal. Exiting...", style="yellow")
                    break
                except EOFError:
                    self.console.print("\n\nReceived EOF. Exiting...", style="yellow")
                    break
                except Exception as e:
                    logger.error(f"Unexpected error in CLI loop: {e}")
                    self.display_error(f"Unexpected error: {str(e)}")
        
        finally:
            self.running = False
            logger.info("CLI interface stopped")


async def main():
    """Main entry point for CLI"""
    try:
        cli = CLIInterface()
        await cli.run()
    except Exception as e:
        print(f"Failed to start JARVIS CLI: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
