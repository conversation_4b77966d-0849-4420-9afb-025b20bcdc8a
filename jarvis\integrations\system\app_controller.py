"""
Application Controller for JARVIS AI Assistant
"""

import os
import subprocess
import psutil
import time
import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import platform

from ...config import config

logger = logging.getLogger(__name__)


class ApplicationController:
    """Controls desktop applications and processes"""
    
    def __init__(self):
        """Initialize application controller"""
        self.safe_mode = config.get("system.app_control.safe_mode", True)
        self.allowed_apps = set(config.get("system.app_control.allowed_apps", [
            "notepad", "calculator", "chrome", "firefox", "code", "explorer"
        ]))
        self.system_type = platform.system().lower()
        
        # Common application paths by OS
        self.app_paths = self._get_common_app_paths()
        
        logger.info(f"Application controller initialized - Safe mode: {self.safe_mode}")
    
    def _get_common_app_paths(self) -> Dict[str, str]:
        """Get common application paths for the current OS"""
        if self.system_type == "windows":
            return {
                "notepad": "notepad.exe",
                "calculator": "calc.exe",
                "chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "firefox": r"C:\Program Files\Mozilla Firefox\firefox.exe",
                "code": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv("USERNAME", "")),
                "explorer": "explorer.exe",
                "cmd": "cmd.exe",
                "powershell": "powershell.exe"
            }
        elif self.system_type == "darwin":  # macOS
            return {
                "safari": "/Applications/Safari.app",
                "chrome": "/Applications/Google Chrome.app",
                "firefox": "/Applications/Firefox.app",
                "code": "/Applications/Visual Studio Code.app",
                "finder": "/System/Library/CoreServices/Finder.app",
                "terminal": "/Applications/Utilities/Terminal.app"
            }
        else:  # Linux
            return {
                "firefox": "firefox",
                "chrome": "google-chrome",
                "code": "code",
                "nautilus": "nautilus",
                "terminal": "gnome-terminal"
            }
    
    def is_app_allowed(self, app_name: str) -> bool:
        """Check if application is allowed to be controlled"""
        if not self.safe_mode:
            return True
        
        app_name_lower = app_name.lower()
        return any(allowed in app_name_lower for allowed in self.allowed_apps)
    
    def launch_application(
        self, 
        app_name: str, 
        args: Optional[List[str]] = None,
        wait_for_start: bool = True,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """
        Launch an application
        
        Args:
            app_name: Application name or path
            args: Command line arguments
            wait_for_start: Wait for application to start
            timeout: Timeout in seconds
            
        Returns:
            Dictionary with launch result
        """
        try:
            if not self.is_app_allowed(app_name):
                return {
                    "success": False,
                    "error": f"Application not allowed in safe mode: {app_name}"
                }
            
            # Resolve application path
            app_path = self._resolve_app_path(app_name)
            if not app_path:
                return {
                    "success": False,
                    "error": f"Application not found: {app_name}"
                }
            
            # Prepare command
            command = [app_path]
            if args:
                command.extend(args)
            
            # Launch application
            if self.system_type == "windows":
                process = subprocess.Popen(
                    command,
                    creationflags=subprocess.CREATE_NEW_CONSOLE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            else:
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    start_new_session=True
                )
            
            result = {
                "success": True,
                "app_name": app_name,
                "app_path": app_path,
                "process_id": process.pid,
                "command": command
            }
            
            # Wait for application to start if requested
            if wait_for_start:
                start_time = time.time()
                while time.time() - start_time < timeout:
                    if self._is_process_running(process.pid):
                        result["started"] = True
                        break
                    time.sleep(0.5)
                else:
                    result["started"] = False
                    result["warning"] = "Application may not have started properly"
            
            logger.info(f"Launched application: {app_name} (PID: {process.pid})")
            return result
            
        except Exception as e:
            logger.error(f"Error launching application {app_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "app_name": app_name
            }
    
    def close_application(self, identifier: Union[str, int], force: bool = False) -> Dict[str, Any]:
        """
        Close an application by name or PID
        
        Args:
            identifier: Application name or process ID
            force: Force close if normal close fails
            
        Returns:
            Dictionary with close result
        """
        try:
            processes = []
            
            if isinstance(identifier, int):
                # Close by PID
                try:
                    process = psutil.Process(identifier)
                    processes = [process]
                except psutil.NoSuchProcess:
                    return {
                        "success": False,
                        "error": f"Process with PID {identifier} not found"
                    }
            else:
                # Close by name
                if not self.is_app_allowed(identifier):
                    return {
                        "success": False,
                        "error": f"Application not allowed in safe mode: {identifier}"
                    }
                
                processes = [p for p in psutil.process_iter(['pid', 'name']) 
                           if identifier.lower() in p.info['name'].lower()]
                
                if not processes:
                    return {
                        "success": False,
                        "error": f"No running processes found for: {identifier}"
                    }
            
            closed_processes = []
            failed_processes = []
            
            for process in processes:
                try:
                    process_info = {
                        "pid": process.pid,
                        "name": process.name(),
                        "status": process.status()
                    }
                    
                    if force:
                        process.kill()
                        process_info["method"] = "killed"
                    else:
                        process.terminate()
                        process_info["method"] = "terminated"
                        
                        # Wait for graceful termination
                        try:
                            process.wait(timeout=5)
                        except psutil.TimeoutExpired:
                            process.kill()
                            process_info["method"] = "killed_after_timeout"
                    
                    closed_processes.append(process_info)
                    
                except Exception as e:
                    failed_processes.append({
                        "pid": process.pid,
                        "error": str(e)
                    })
            
            result = {
                "success": len(closed_processes) > 0,
                "identifier": identifier,
                "closed_processes": closed_processes,
                "failed_processes": failed_processes,
                "total_closed": len(closed_processes)
            }
            
            if closed_processes:
                logger.info(f"Closed {len(closed_processes)} processes for: {identifier}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error closing application {identifier}: {e}")
            return {
                "success": False,
                "error": str(e),
                "identifier": identifier
            }
    
    def list_running_applications(self, filter_name: Optional[str] = None) -> Dict[str, Any]:
        """
        List running applications
        
        Args:
            filter_name: Filter by application name
            
        Returns:
            Dictionary with running applications
        """
        try:
            applications = []
            
            for process in psutil.process_iter(['pid', 'name', 'status', 'create_time', 'memory_info', 'cpu_percent']):
                try:
                    process_info = process.info
                    
                    # Filter by name if specified
                    if filter_name and filter_name.lower() not in process_info['name'].lower():
                        continue
                    
                    # Skip system processes in safe mode
                    if self.safe_mode and not self.is_app_allowed(process_info['name']):
                        continue
                    
                    app_info = {
                        "pid": process_info['pid'],
                        "name": process_info['name'],
                        "status": process_info['status'],
                        "memory_mb": round(process_info['memory_info'].rss / 1024 / 1024, 1),
                        "cpu_percent": process_info['cpu_percent'],
                        "started": time.strftime('%Y-%m-%d %H:%M:%S', 
                                             time.localtime(process_info['create_time']))
                    }
                    
                    applications.append(app_info)
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by memory usage (descending)
            applications.sort(key=lambda x: x['memory_mb'], reverse=True)
            
            return {
                "success": True,
                "applications": applications,
                "count": len(applications),
                "filter": filter_name,
                "safe_mode": self.safe_mode
            }
            
        except Exception as e:
            logger.error(f"Error listing applications: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_application_info(self, identifier: Union[str, int]) -> Dict[str, Any]:
        """Get detailed information about an application"""
        try:
            process = None
            
            if isinstance(identifier, int):
                try:
                    process = psutil.Process(identifier)
                except psutil.NoSuchProcess:
                    return {
                        "success": False,
                        "error": f"Process with PID {identifier} not found"
                    }
            else:
                # Find by name
                processes = [p for p in psutil.process_iter(['pid', 'name']) 
                           if identifier.lower() in p.info['name'].lower()]
                
                if not processes:
                    return {
                        "success": False,
                        "error": f"No running processes found for: {identifier}"
                    }
                
                process = processes[0]  # Take first match
            
            # Get detailed process information
            info = {
                "success": True,
                "pid": process.pid,
                "name": process.name(),
                "status": process.status(),
                "started": time.strftime('%Y-%m-%d %H:%M:%S', 
                                       time.localtime(process.create_time())),
                "memory": {
                    "rss_mb": round(process.memory_info().rss / 1024 / 1024, 1),
                    "vms_mb": round(process.memory_info().vms / 1024 / 1024, 1),
                    "percent": round(process.memory_percent(), 2)
                },
                "cpu_percent": process.cpu_percent(),
                "num_threads": process.num_threads()
            }
            
            # Add additional info if available
            try:
                info["exe_path"] = process.exe()
                info["cwd"] = process.cwd()
                info["cmdline"] = process.cmdline()
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting application info for {identifier}: {e}")
            return {
                "success": False,
                "error": str(e),
                "identifier": identifier
            }
    
    def _resolve_app_path(self, app_name: str) -> Optional[str]:
        """Resolve application name to full path"""
        # Check if it's already a full path
        if os.path.isfile(app_name):
            return app_name
        
        # Check common app paths
        if app_name.lower() in self.app_paths:
            app_path = self.app_paths[app_name.lower()]
            if os.path.isfile(app_path):
                return app_path
        
        # Try to find in PATH
        if self.system_type == "windows":
            # Add .exe if not present
            if not app_name.endswith('.exe'):
                app_name += '.exe'
        
        # Search in PATH
        for path_dir in os.environ.get('PATH', '').split(os.pathsep):
            full_path = os.path.join(path_dir, app_name)
            if os.path.isfile(full_path):
                return full_path
        
        return None
    
    def _is_process_running(self, pid: int) -> bool:
        """Check if process is running"""
        try:
            process = psutil.Process(pid)
            return process.is_running()
        except psutil.NoSuchProcess:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get application controller statistics"""
        running_count = len(list(psutil.process_iter()))
        
        return {
            "safe_mode": self.safe_mode,
            "allowed_apps": list(self.allowed_apps),
            "system_type": self.system_type,
            "total_running_processes": running_count,
            "available_apps": list(self.app_paths.keys())
        }
