"""
Comprehensive Test Runner for JARVIS AI Assistant
"""

import unittest
import asyncio
import logging
import time
import sys
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
import json
import traceback

logger = logging.getLogger(__name__)


@dataclass
class TestResult:
    """Test result representation"""
    test_name: str
    test_type: str  # unit, integration, performance, security
    status: str  # passed, failed, skipped, error
    duration: float
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    error_traceback: Optional[str] = None


@dataclass
class TestSuiteResult:
    """Test suite result representation"""
    suite_name: str
    total_tests: int
    passed: int
    failed: int
    skipped: int
    errors: int
    duration: float
    results: List[TestResult] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


class TestRunner:
    """Comprehensive test runner for JARVIS"""
    
    def __init__(self, jarvis_core=None, output_dir: Optional[str] = None):
        """Initialize test runner"""
        self.jarvis_core = jarvis_core
        self.output_dir = Path(output_dir or "test_results")
        self.output_dir.mkdir(exist_ok=True)
        
        # Test configuration
        self.test_timeout = 30  # seconds
        self.parallel_tests = False
        self.verbose = True
        
        # Test registry
        self.test_suites: Dict[str, Any] = {}
        self.test_functions: Dict[str, Callable] = {}
        
        # Results
        self.suite_results: List[TestSuiteResult] = []
        
        logger.info("Test runner initialized")
    
    def register_test_suite(self, name: str, test_suite):
        """Register a test suite"""
        self.test_suites[name] = test_suite
        logger.info(f"Registered test suite: {name}")
    
    def register_test_function(self, name: str, test_func: Callable):
        """Register a standalone test function"""
        self.test_functions[name] = test_func
        logger.info(f"Registered test function: {name}")
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all registered tests"""
        logger.info("Starting comprehensive test run")
        start_time = time.time()
        
        # Run test suites
        for suite_name, test_suite in self.test_suites.items():
            try:
                result = await self._run_test_suite(suite_name, test_suite)
                self.suite_results.append(result)
            except Exception as e:
                logger.error(f"Error running test suite {suite_name}: {e}")
        
        # Run standalone test functions
        if self.test_functions:
            standalone_results = []
            for func_name, test_func in self.test_functions.items():
                try:
                    result = await self._run_test_function(func_name, test_func)
                    standalone_results.append(result)
                except Exception as e:
                    logger.error(f"Error running test function {func_name}: {e}")
            
            if standalone_results:
                suite_result = TestSuiteResult(
                    suite_name="standalone_tests",
                    total_tests=len(standalone_results),
                    passed=sum(1 for r in standalone_results if r.status == "passed"),
                    failed=sum(1 for r in standalone_results if r.status == "failed"),
                    skipped=sum(1 for r in standalone_results if r.status == "skipped"),
                    errors=sum(1 for r in standalone_results if r.status == "error"),
                    duration=sum(r.duration for r in standalone_results),
                    results=standalone_results
                )
                self.suite_results.append(suite_result)
        
        total_duration = time.time() - start_time
        
        # Generate summary
        summary = self._generate_test_summary(total_duration)
        
        # Save results
        await self._save_test_results(summary)
        
        # Print summary
        self._print_test_summary(summary)
        
        return summary
    
    async def run_test_suite(self, suite_name: str) -> Optional[TestSuiteResult]:
        """Run a specific test suite"""
        if suite_name not in self.test_suites:
            logger.error(f"Test suite not found: {suite_name}")
            return None
        
        test_suite = self.test_suites[suite_name]
        return await self._run_test_suite(suite_name, test_suite)
    
    async def run_test_by_type(self, test_type: str) -> Dict[str, Any]:
        """Run tests of a specific type (unit, integration, performance, security)"""
        logger.info(f"Running {test_type} tests")
        
        filtered_results = []
        
        for suite_name, test_suite in self.test_suites.items():
            if hasattr(test_suite, 'test_type') and test_suite.test_type == test_type:
                result = await self._run_test_suite(suite_name, test_suite)
                filtered_results.append(result)
        
        # Generate summary for filtered results
        total_tests = sum(r.total_tests for r in filtered_results)
        total_passed = sum(r.passed for r in filtered_results)
        total_failed = sum(r.failed for r in filtered_results)
        total_skipped = sum(r.skipped for r in filtered_results)
        total_errors = sum(r.errors for r in filtered_results)
        total_duration = sum(r.duration for r in filtered_results)
        
        return {
            "test_type": test_type,
            "total_tests": total_tests,
            "passed": total_passed,
            "failed": total_failed,
            "skipped": total_skipped,
            "errors": total_errors,
            "duration": total_duration,
            "success_rate": (total_passed / total_tests * 100) if total_tests > 0 else 0,
            "suite_results": filtered_results
        }
    
    async def _run_test_suite(self, suite_name: str, test_suite) -> TestSuiteResult:
        """Run a test suite"""
        logger.info(f"Running test suite: {suite_name}")
        start_time = time.time()
        
        results = []
        
        # Get test methods
        test_methods = [method for method in dir(test_suite) if method.startswith('test_')]
        
        for method_name in test_methods:
            test_method = getattr(test_suite, method_name)
            result = await self._run_single_test(f"{suite_name}.{method_name}", test_method)
            results.append(result)
        
        duration = time.time() - start_time
        
        # Calculate statistics
        passed = sum(1 for r in results if r.status == "passed")
        failed = sum(1 for r in results if r.status == "failed")
        skipped = sum(1 for r in results if r.status == "skipped")
        errors = sum(1 for r in results if r.status == "error")
        
        suite_result = TestSuiteResult(
            suite_name=suite_name,
            total_tests=len(results),
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            duration=duration,
            results=results
        )
        
        logger.info(f"Test suite {suite_name} completed: {passed}/{len(results)} passed")
        return suite_result
    
    async def _run_test_function(self, func_name: str, test_func: Callable) -> TestResult:
        """Run a standalone test function"""
        return await self._run_single_test(func_name, test_func)
    
    async def _run_single_test(self, test_name: str, test_func: Callable) -> TestResult:
        """Run a single test"""
        start_time = time.time()
        
        try:
            if self.verbose:
                print(f"Running {test_name}...", end=" ")
            
            # Run test with timeout
            if asyncio.iscoroutinefunction(test_func):
                await asyncio.wait_for(test_func(), timeout=self.test_timeout)
            else:
                test_func()
            
            duration = time.time() - start_time
            
            if self.verbose:
                print(f"PASSED ({duration:.3f}s)")
            
            return TestResult(
                test_name=test_name,
                test_type="unit",  # Default type
                status="passed",
                duration=duration,
                message="Test passed successfully"
            )
            
        except unittest.SkipTest as e:
            duration = time.time() - start_time
            
            if self.verbose:
                print(f"SKIPPED ({str(e)})")
            
            return TestResult(
                test_name=test_name,
                test_type="unit",
                status="skipped",
                duration=duration,
                message=str(e)
            )
            
        except AssertionError as e:
            duration = time.time() - start_time
            
            if self.verbose:
                print(f"FAILED ({str(e)})")
            
            return TestResult(
                test_name=test_name,
                test_type="unit",
                status="failed",
                duration=duration,
                message=str(e),
                error_traceback=traceback.format_exc()
            )
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            
            if self.verbose:
                print(f"TIMEOUT ({duration:.3f}s)")
            
            return TestResult(
                test_name=test_name,
                test_type="unit",
                status="error",
                duration=duration,
                message=f"Test timed out after {self.test_timeout} seconds",
                error_traceback="TimeoutError"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            
            if self.verbose:
                print(f"ERROR ({str(e)})")
            
            return TestResult(
                test_name=test_name,
                test_type="unit",
                status="error",
                duration=duration,
                message=str(e),
                error_traceback=traceback.format_exc()
            )
    
    def _generate_test_summary(self, total_duration: float) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        total_tests = sum(r.total_tests for r in self.suite_results)
        total_passed = sum(r.passed for r in self.suite_results)
        total_failed = sum(r.failed for r in self.suite_results)
        total_skipped = sum(r.skipped for r in self.suite_results)
        total_errors = sum(r.errors for r in self.suite_results)
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # Group results by type
        results_by_type = {}
        for suite_result in self.suite_results:
            for test_result in suite_result.results:
                test_type = test_result.test_type
                if test_type not in results_by_type:
                    results_by_type[test_type] = {"passed": 0, "failed": 0, "skipped": 0, "errors": 0}
                results_by_type[test_type][test_result.status] += 1
        
        # Find slowest tests
        all_results = []
        for suite_result in self.suite_results:
            all_results.extend(suite_result.results)
        
        slowest_tests = sorted(all_results, key=lambda r: r.duration, reverse=True)[:10]
        
        # Find failed tests
        failed_tests = [r for r in all_results if r.status in ["failed", "error"]]
        
        return {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": total_passed,
                "failed": total_failed,
                "skipped": total_skipped,
                "errors": total_errors,
                "success_rate": round(success_rate, 2),
                "total_duration": round(total_duration, 3)
            },
            "results_by_type": results_by_type,
            "suite_results": [
                {
                    "suite_name": r.suite_name,
                    "total_tests": r.total_tests,
                    "passed": r.passed,
                    "failed": r.failed,
                    "skipped": r.skipped,
                    "errors": r.errors,
                    "duration": round(r.duration, 3),
                    "success_rate": round((r.passed / r.total_tests * 100) if r.total_tests > 0 else 0, 2)
                }
                for r in self.suite_results
            ],
            "slowest_tests": [
                {
                    "test_name": r.test_name,
                    "duration": round(r.duration, 3),
                    "status": r.status
                }
                for r in slowest_tests
            ],
            "failed_tests": [
                {
                    "test_name": r.test_name,
                    "status": r.status,
                    "message": r.message,
                    "duration": round(r.duration, 3)
                }
                for r in failed_tests
            ]
        }
    
    async def _save_test_results(self, summary: Dict[str, Any]):
        """Save test results to files"""
        try:
            # Save summary
            summary_file = self.output_dir / f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            # Save detailed results
            detailed_results = []
            for suite_result in self.suite_results:
                for test_result in suite_result.results:
                    detailed_results.append({
                        "suite_name": suite_result.suite_name,
                        "test_name": test_result.test_name,
                        "test_type": test_result.test_type,
                        "status": test_result.status,
                        "duration": test_result.duration,
                        "message": test_result.message,
                        "timestamp": test_result.timestamp.isoformat(),
                        "error_traceback": test_result.error_traceback
                    })
            
            detailed_file = self.output_dir / f"test_details_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(detailed_file, 'w') as f:
                json.dump(detailed_results, f, indent=2)
            
            logger.info(f"Test results saved to {self.output_dir}")
            
        except Exception as e:
            logger.error(f"Error saving test results: {e}")
    
    def _print_test_summary(self, summary: Dict[str, Any]):
        """Print test summary to console"""
        print("\n" + "="*80)
        print("JARVIS TEST SUMMARY")
        print("="*80)
        
        s = summary["summary"]
        print(f"Total Tests: {s['total_tests']}")
        print(f"Passed: {s['passed']} ({s['success_rate']:.1f}%)")
        print(f"Failed: {s['failed']}")
        print(f"Skipped: {s['skipped']}")
        print(f"Errors: {s['errors']}")
        print(f"Duration: {s['total_duration']:.3f}s")
        
        # Print suite results
        print("\nSUITE RESULTS:")
        print("-" * 80)
        for suite in summary["suite_results"]:
            status = "✓" if suite["failed"] == 0 and suite["errors"] == 0 else "✗"
            print(f"{status} {suite['suite_name']}: {suite['passed']}/{suite['total_tests']} "
                  f"({suite['success_rate']:.1f}%) - {suite['duration']:.3f}s")
        
        # Print failed tests if any
        if summary["failed_tests"]:
            print("\nFAILED TESTS:")
            print("-" * 80)
            for test in summary["failed_tests"]:
                print(f"✗ {test['test_name']}: {test['message']}")
        
        # Overall result
        print("\n" + "="*80)
        if s['failed'] == 0 and s['errors'] == 0:
            print("🎉 ALL TESTS PASSED!")
        else:
            print(f"❌ {s['failed'] + s['errors']} TESTS FAILED")
        print("="*80)
    
    def generate_html_report(self, output_file: Optional[str] = None) -> str:
        """Generate HTML test report"""
        if not output_file:
            output_file = self.output_dir / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        # Generate HTML content
        html_content = self._generate_html_content()
        
        with open(output_file, 'w') as f:
            f.write(html_content)
        
        logger.info(f"HTML report generated: {output_file}")
        return str(output_file)
    
    def _generate_html_content(self) -> str:
        """Generate HTML content for test report"""
        # This would generate a comprehensive HTML report
        # For brevity, returning a simple template
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>JARVIS Test Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .passed { color: green; }
                .failed { color: red; }
                .skipped { color: orange; }
                .error { color: darkred; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>JARVIS Test Report</h1>
                <p>Generated: {timestamp}</p>
            </div>
            <!-- Test results would be inserted here -->
        </body>
        </html>
        """.format(timestamp=datetime.now().isoformat())
    
    def cleanup(self):
        """Cleanup test runner resources"""
        logger.info("Test runner cleanup completed")
