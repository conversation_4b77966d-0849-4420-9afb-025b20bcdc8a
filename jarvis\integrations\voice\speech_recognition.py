"""
Speech Recognition for JARVIS AI Assistant
"""

import logging
import asyncio
from typing import Dict, List, Any, Union
from pathlib import Path
import tempfile
import wave

from ...config import config

logger = logging.getLogger(__name__)


class SpeechRecognizer:
    """Multi-provider speech recognition system"""
    
    def __init__(self):
        """Initialize speech recognizer"""
        self.provider = config.get("voice.recognition.provider", "whisper")
        self.language = config.get("voice.recognition.language", "en-US")
        self.sample_rate = config.get("voice.recognition.sample_rate", 16000)
        self.chunk_size = config.get("voice.recognition.chunk_size", 1024)
        
        # Initialize providers
        self.whisper_model = None
        self.google_recognizer = None
        self.azure_recognizer = None
        
        self._init_providers()
        
        logger.info(f"Speech recognizer initialized with provider: {self.provider}")
    
    def _init_providers(self):
        """Initialize available speech recognition providers"""
        # Initialize OpenAI Whisper
        try:
            import whisper  # type: ignore
            model_size = config.get("voice.recognition.whisper_model", "base")
            self.whisper_model = whisper.load_model(model_size) # type: ignore
            logger.info(f"Whisper model loaded: {model_size}")
        except ImportError:
            logger.warning("Whisper not available. Install with: pip install openai-whisper")
            self.whisper_model = None
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            self.whisper_model = None

        # Initialize Google Speech Recognition
        try:
            import speech_recognition as sr
            self.google_recognizer = sr.Recognizer()
            logger.info("Google Speech Recognition initialized")
        except ImportError:
            logger.warning("SpeechRecognition not available. Install with: pip install SpeechRecognition")
            self.google_recognizer = None
        except Exception as e:
            logger.error(f"Failed to initialize Google Speech Recognition: {e}")
            self.google_recognizer = None

        # Initialize Azure Speech
        try:
            import azure.cognitiveservices.speech as speechsdk  # type: ignore
            speech_key = config.get_api_key("azure_speech")
            speech_region = config.get("voice.recognition.azure_region", "eastus")

            if speech_key:
                speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=speech_region)
                speech_config.speech_recognition_language = self.language
                self.azure_recognizer = speech_config
                logger.info("Azure Speech Recognition initialized")
            else:
                logger.warning("Azure Speech API key not configured")
                self.azure_recognizer = None
        except ImportError:
            logger.warning("Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech")
            self.azure_recognizer = None
        except Exception as e:
            logger.error(f"Failed to initialize Azure Speech Recognition: {e}")
            self.azure_recognizer = None
    
    async def recognize_from_file(self, audio_file: Union[str, Path]) -> Dict[str, Any]:
        """
        Recognize speech from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Dictionary with recognition result
        """
        try:
            audio_path = Path(audio_file)
            
            if not audio_path.exists():
                return {
                    "success": False,
                    "error": f"Audio file not found: {audio_file}"
                }
            
            if self.provider == "whisper" and self.whisper_model:
                return await self._recognize_whisper(audio_path)
            elif self.provider == "google" and self.google_recognizer:
                return await self._recognize_google(audio_path)
            elif self.provider == "azure" and self.azure_recognizer:
                return await self._recognize_azure(audio_path)
            else:
                return {
                    "success": False,
                    "error": f"Provider {self.provider} not available"
                }
                
        except Exception as e:
            logger.error(f"Error recognizing speech from file: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _recognize_whisper(self, audio_path: Path) -> Dict[str, Any]:
        """Recognize speech using OpenAI Whisper"""
        if not self.whisper_model:
            return {
                "success": False,
                "error": "Whisper model not available",
                "provider": "whisper"
            }

        try:
            # Run Whisper in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.whisper_model.transcribe,
                str(audio_path)
            )

            return {
                "success": True,
                "text": result["text"].strip(),
                "language": result.get("language", "unknown"),
                "confidence": 1.0,  # Whisper doesn't provide confidence scores
                "provider": "whisper",
                "segments": result.get("segments", [])
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "whisper"
            }
    
    async def _recognize_google(self, audio_path: Path) -> Dict[str, Any]:
        """Recognize speech using Google Speech Recognition"""
        if not self.google_recognizer:
            return {
                "success": False,
                "error": "Google Speech Recognition not available",
                "provider": "google"
            }

        try:
            import speech_recognition as sr

            # Load audio file
            with sr.AudioFile(str(audio_path)) as source:
                audio = self.google_recognizer.record(source)

            # Recognize speech
            loop = asyncio.get_event_loop()
            text = await loop.run_in_executor(
                None,
                self.google_recognizer.recognize_google,  # type: ignore
                audio,
                None,  # API key (None for free tier)
                self.language
            )

            return {
                "success": True,
                "text": text,
                "language": self.language,
                "confidence": 1.0,  # Google API doesn't return confidence in free tier
                "provider": "google"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "google"
            }
    
    async def _recognize_azure(self, audio_path: Path) -> Dict[str, Any]:
        """Recognize speech using Azure Speech Services"""
        if not self.azure_recognizer:
            return {
                "success": False,
                "error": "Azure Speech Recognition not available",
                "provider": "azure"
            }

        try:
            import azure.cognitiveservices.speech as speechsdk  # type: ignore

            # Create audio config
            audio_config = speechsdk.audio.AudioConfig(filename=str(audio_path))  # type: ignore

            # Create speech recognizer
            speech_recognizer = speechsdk.SpeechRecognizer(  # type: ignore
                speech_config=self.azure_recognizer,
                audio_config=audio_config
            )

            # Perform recognition
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                speech_recognizer.recognize_once
            )

            if result.reason == speechsdk.ResultReason.RecognizedSpeech:  # type: ignore
                return {
                    "success": True,
                    "text": result.text,
                    "language": self.language,
                    "confidence": result.confidence if hasattr(result, 'confidence') else 1.0, # type: ignore
                    "provider": "azure"
                }
            else:
                return {
                    "success": False,
                    "error": f"Recognition failed: {result.reason}",
                    "provider": "azure"
                }

        except ImportError:
            return {
                "success": False,
                "error": "Azure Speech SDK not available",
                "provider": "azure"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "azure"
            }
    
    async def recognize_from_microphone(self, duration: float = 5.0) -> Dict[str, Any]:
        """
        Recognize speech from microphone
        
        Args:
            duration: Recording duration in seconds
            
        Returns:
            Dictionary with recognition result
        """
        try:
            # Record audio from microphone
            audio_data = await self._record_audio(duration)
            
            if not audio_data["success"]:
                return audio_data
            
            # Save to temporary file and recognize
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = Path(temp_file.name)
                
                # Write audio data to file
                with wave.open(str(temp_path), 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data["audio_data"])
                
                # Recognize speech
                result = await self.recognize_from_file(temp_path)
                
                # Clean up
                temp_path.unlink()
                
                return result
                
        except Exception as e:
            logger.error(f"Error recognizing speech from microphone: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _record_audio(self, duration: float) -> Dict[str, Any]:
        """Record audio from microphone"""
        try:
            import pyaudio  # type: ignore

            # Audio recording parameters
            audio_format = pyaudio.paInt16  # type: ignore
            channels = 1
            rate = self.sample_rate
            chunk = self.chunk_size

            # Initialize PyAudio
            audio = pyaudio.PyAudio()  # type: ignore

            # Open stream
            stream = audio.open(
                format=audio_format,
                channels=channels,
                rate=rate,
                input=True,
                frames_per_buffer=chunk
            )

            logger.info(f"Recording audio for {duration} seconds...")

            frames = []
            for _ in range(int(rate / chunk * duration)):
                data = stream.read(chunk)
                frames.append(data)

            # Stop and close stream
            stream.stop_stream()
            stream.close()
            audio.terminate()

            # Combine audio data
            audio_data = b''.join(frames)

            return {
                "success": True,
                "audio_data": audio_data,
                "duration": duration,
                "sample_rate": rate,
                "channels": channels
            }

        except ImportError:
            return {
                "success": False,
                "error": "PyAudio not available. Install with: pip install pyaudio"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def start_continuous_recognition(self, callback=None) -> Dict[str, Any]:
        """
        Start continuous speech recognition
        
        Args:
            callback: Function to call with recognition results
            
        Returns:
            Dictionary with operation result
        """
        try:
            if self.provider == "azure" and self.azure_recognizer:
                return await self._start_azure_continuous(callback)
            else:
                return await self._start_manual_continuous(callback)
                
        except Exception as e:
            logger.error(f"Error starting continuous recognition: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _start_azure_continuous(self, callback=None) -> Dict[str, Any]:
        """Start continuous recognition with Azure"""
        if not self.azure_recognizer:
            return {
                "success": False,
                "error": "Azure Speech Recognition not available"
            }

        try:
            import azure.cognitiveservices.speech as speechsdk  # type: ignore

            # Create audio config for microphone
            audio_config = speechsdk.audio.AudioConfig(use_default_microphone=True)  # type: ignore

            # Create speech recognizer
            speech_recognizer = speechsdk.SpeechRecognizer(  # type: ignore
                speech_config=self.azure_recognizer,
                audio_config=audio_config
            )

            # Set up event handlers
            def recognized_handler(evt):
                if callback and evt.result.text:
                    callback({
                        "success": True,
                        "text": evt.result.text,
                        "provider": "azure_continuous"
                    })

            speech_recognizer.recognized.connect(recognized_handler)

            # Start continuous recognition
            speech_recognizer.start_continuous_recognition()

            return {
                "success": True,
                "message": "Continuous recognition started",
                "recognizer": speech_recognizer
            }

        except ImportError:
            return {
                "success": False,
                "error": "Azure Speech SDK not available"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _start_manual_continuous(self, callback=None) -> Dict[str, Any]:
        """Start manual continuous recognition loop"""
        try:
            logger.info("Starting manual continuous recognition...")
            
            while True:
                # Record short audio chunks
                result = await self.recognize_from_microphone(duration=2.0)
                
                if result["success"] and result.get("text", "").strip():
                    if callback:
                        callback(result)
                
                # Short pause between recordings
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            return {
                "success": True,
                "message": "Continuous recognition stopped"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_available_providers(self) -> List[str]:
        """Get list of available speech recognition providers"""
        providers = []
        
        if self.whisper_model:
            providers.append("whisper")
        if self.google_recognizer:
            providers.append("google")
        if self.azure_recognizer:
            providers.append("azure")
            
        return providers
    
    def get_stats(self) -> Dict[str, Any]:
        """Get speech recognizer statistics"""
        return {
            "provider": self.provider,
            "language": self.language,
            "sample_rate": self.sample_rate,
            "available_providers": self.get_available_providers(),
            "whisper_available": self.whisper_model is not None,
            "google_available": self.google_recognizer is not None,
            "azure_available": self.azure_recognizer is not None
        }
