"""
Context Manager for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class ConversationContext:
    """Context for a conversation"""
    session_id: str
    user_id: Optional[str] = None
    start_time: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    message_count: int = 0
    current_topic: Optional[str] = None
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    conversation_state: Dict[str, Any] = field(default_factory=dict)
    active_tasks: List[str] = field(default_factory=list)
    recent_intents: List[str] = field(default_factory=list)
    context_variables: Dict[str, Any] = field(default_factory=dict)


class ContextManager:
    """Manages conversation context and user state"""
    
    def __init__(self, memory_manager=None):
        """Initialize context manager"""
        self.memory_manager = memory_manager
        self.active_contexts: Dict[str, ConversationContext] = {}
        self.context_timeout = timedelta(hours=2)  # Context expires after 2 hours
        
        logger.info("Context manager initialized")
    
    def get_or_create_context(self, session_id: str, user_id: Optional[str] = None) -> ConversationContext:
        """Get existing context or create new one"""
        if session_id in self.active_contexts:
            context = self.active_contexts[session_id]
            context.last_activity = datetime.now()
            return context
        
        # Create new context
        context = ConversationContext(
            session_id=session_id,
            user_id=user_id
        )
        
        # Load user preferences if available
        if user_id and self.memory_manager:
            preferences = self.memory_manager.get_personal_info("user_preferences", {})
            context.user_preferences = preferences
        
        self.active_contexts[session_id] = context
        logger.info(f"Created new context for session: {session_id}")
        return context
    
    def update_context(
        self, 
        session_id: str, 
        message: str,
        intent: Optional[str] = None,
        entities: Optional[Dict[str, Any]] = None,
        task_id: Optional[str] = None
    ):
        """Update context with new information"""
        context = self.get_or_create_context(session_id)
        
        # Update basic stats
        context.message_count += 1
        context.last_activity = datetime.now()
        
        # Update recent intents
        if intent:
            context.recent_intents.append(intent)
            # Keep only last 10 intents
            context.recent_intents = context.recent_intents[-10:]
            
            # Update current topic based on intent patterns
            context.current_topic = self._determine_topic(context.recent_intents)
        
        # Add active task
        if task_id:
            if task_id not in context.active_tasks:
                context.active_tasks.append(task_id)
        
        # Extract and store context variables from entities
        if entities:
            self._update_context_variables(context, entities)
        
        # Store contextual information in memory
        if self.memory_manager:
            contextual_info = {
                "session_id": session_id,
                "message_count": context.message_count,
                "current_topic": context.current_topic,
                "recent_intents": context.recent_intents[-3:],  # Last 3 intents
                "timestamp": datetime.now().isoformat()
            }
            
            self.memory_manager.add_semantic_memory(
                content=f"Context update: {message}",
                memory_type="context",
                metadata=contextual_info
            )
    
    def get_context_for_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """Get relevant context for processing a message"""
        context = self.get_or_create_context(session_id)
        
        # Build context dictionary
        message_context = {
            "session_id": session_id,
            "user_id": context.user_id,
            "message_count": context.message_count,
            "current_topic": context.current_topic,
            "recent_intents": context.recent_intents,
            "user_preferences": context.user_preferences,
            "conversation_state": context.conversation_state,
            "active_tasks": context.active_tasks,
            "context_variables": context.context_variables,
            "session_duration": (datetime.now() - context.start_time).total_seconds()
        }
        
        # Add relevant memories if available
        if self.memory_manager:
            relevant_memories = self.memory_manager.get_contextual_memories(message)
            message_context["relevant_memories"] = relevant_memories
        
        return message_context
    
    def set_user_preference(self, session_id: str, key: str, value: Any):
        """Set a user preference"""
        context = self.get_or_create_context(session_id)
        context.user_preferences[key] = value
        
        # Persist to memory if available
        if self.memory_manager and context.user_id:
            all_preferences = self.memory_manager.get_personal_info("user_preferences", {})
            all_preferences[key] = value
            self.memory_manager.store_personal_info("user_preferences", all_preferences)
    
    def get_user_preference(self, session_id: str, key: str, default: Any = None) -> Any:
        """Get a user preference"""
        context = self.get_or_create_context(session_id)
        return context.user_preferences.get(key, default)
    
    def set_conversation_state(self, session_id: str, key: str, value: Any):
        """Set conversation state variable"""
        context = self.get_or_create_context(session_id)
        context.conversation_state[key] = value
    
    def get_conversation_state(self, session_id: str, key: str, default: Any = None) -> Any:
        """Get conversation state variable"""
        context = self.get_or_create_context(session_id)
        return context.conversation_state.get(key, default)
    
    def complete_task(self, session_id: str, task_id: str):
        """Mark a task as completed"""
        if session_id in self.active_contexts:
            context = self.active_contexts[session_id]
            if task_id in context.active_tasks:
                context.active_tasks.remove(task_id)
    
    def get_conversation_summary(self, session_id: str) -> Dict[str, Any]:
        """Get summary of conversation context"""
        if session_id not in self.active_contexts:
            return {"status": "no_active_context"}
        
        context = self.active_contexts[session_id]
        duration = datetime.now() - context.start_time
        
        return {
            "session_id": session_id,
            "user_id": context.user_id,
            "duration_minutes": duration.total_seconds() / 60,
            "message_count": context.message_count,
            "current_topic": context.current_topic,
            "active_tasks_count": len(context.active_tasks),
            "recent_intents": context.recent_intents[-5:],
            "last_activity": context.last_activity.isoformat()
        }
    
    def cleanup_expired_contexts(self):
        """Remove expired contexts"""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, context in self.active_contexts.items():
            if current_time - context.last_activity > self.context_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.active_contexts[session_id]
            logger.info(f"Cleaned up expired context: {session_id}")
    
    def _determine_topic(self, recent_intents: List[str]) -> Optional[str]:
        """Determine current conversation topic from recent intents"""
        if not recent_intents:
            return None
        
        # Topic mapping based on intent patterns
        topic_mapping = {
            "calculation": "mathematics",
            "get_weather": "weather",
            "file_operation": "file_management",
            "search": "information_search",
            "send_email": "communication",
            "set_reminder": "scheduling",
            "system_control": "system_management",
            "app_control": "application_management",
            "media_control": "media_playback"
        }
        
        # Get most recent intent
        latest_intent = recent_intents[-1]
        return topic_mapping.get(latest_intent, "general")
    
    def _update_context_variables(self, context: ConversationContext, entities: Dict[str, Any]):
        """Update context variables from extracted entities"""
        # Store important entities as context variables
        important_entities = ["person", "location", "time", "date", "file_path", "email"]
        
        for entity_type in important_entities:
            if entity_type in entities and entities[entity_type]:
                # Store the most recent value
                latest_value = entities[entity_type][0]["normalized"]
                context.context_variables[f"last_{entity_type}"] = latest_value
        
        # Store numeric values for calculations
        if "number" in entities:
            numbers = [e["normalized"] for e in entities["number"]]
            context.context_variables["last_numbers"] = numbers
        
        # Store operation types
        if "operation" in entities:
            operation = entities["operation"][0]["normalized"]
            context.context_variables["last_operation"] = operation
    
    def get_stats(self) -> Dict[str, Any]:
        """Get context manager statistics"""
        total_messages = sum(ctx.message_count for ctx in self.active_contexts.values())
        
        return {
            "active_contexts": len(self.active_contexts),
            "total_messages": total_messages,
            "average_messages_per_context": total_messages / len(self.active_contexts) if self.active_contexts else 0,
            "context_timeout_hours": self.context_timeout.total_seconds() / 3600
        }
