2025-07-11 12:25:45,468 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:25:45,469 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:25:46,164 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:25:46,165 - jarvis.core.ai.engine - ERROR - Failed to initialize AI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:26:46,568 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:26:46,571 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:26:47,039 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:26:47,039 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:26:47,040 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:26:47,040 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:26:47,040 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:26:47,041 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:26:47,041 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:27:05,487 - jarvis.core.ai.engine - INFO - Processed message: hello...
2025-07-11 12:27:37,764 - jarvis.core.ai.engine - INFO - Processed message: what is 5 + 3?...
2025-07-11 12:27:52,865 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:27:52,865 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 12:34:35,801 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:34:35,801 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:34:36,518 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:34:36,519 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:34:36,519 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:34:36,520 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:34:36,520 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:34:36,536 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:34:36,536 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:34:36,537 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:34:36,537 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:34:36,537 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:34:36,538 - jarvis.core.memory.storage - INFO - Loaded 2 conversations
2025-07-11 12:34:36,538 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:34:36,539 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:34:54,135 - jarvis.core.ai.engine - INFO - Processed message: what time is it?...
2025-07-11 12:35:30,417 - jarvis.core.ai.engine - INFO - Processed message: calculate 15 + 7...
2025-07-11 12:35:47,402 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:35:47,403 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 12:38:46,307 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:38:46,307 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:38:46,745 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:38:46,745 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:38:46,745 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:38:46,746 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:38:46,746 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:38:46,750 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:38:46,751 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:38:46,751 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:38:46,751 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:38:46,752 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:38:46,757 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 12:38:46,761 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 12:38:46,761 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 12:38:46,761 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 12:38:46,762 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 12:38:46,762 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 12:38:46,762 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 12:38:46,763 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 12:38:46,763 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 12:38:46,763 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 12:38:46,764 - jarvis.core.memory.storage - INFO - Loaded 4 conversations
2025-07-11 12:38:46,764 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:38:46,764 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:39:05,568 - jarvis.core.ai.engine - INFO - Processed message: calculate 15 + 7...
2025-07-11 12:39:28,022 - jarvis.core.ai.engine - INFO - Processed message: what time is it?...
2025-07-11 12:40:02,899 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:40:02,900 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 12:44:17,068 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:44:17,068 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:44:17,497 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:17,497 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:17,497 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:44:17,497 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:44:17,497 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:44:17,501 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:44:17,501 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:44:17,502 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:44:17,502 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:44:17,502 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:44:17,504 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 12:44:17,506 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 12:44:17,506 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 12:44:17,511 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 12:44:17,515 - jarvis.core.memory.storage - INFO - Loaded 6 conversations
2025-07-11 12:44:17,515 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 12:44:17,516 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 12:44:17,516 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 12:44:17,516 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 12:44:17,516 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:44:17,517 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:44:31,937 - jarvis.core.ai.engine - INFO - Processed message: hello...
2025-07-11 12:44:59,003 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:44:59,004 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:44:59,399 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:59,399 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:59,400 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:44:59,400 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:44:59,400 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:44:59,403 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:44:59,404 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:44:59,404 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:44:59,404 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:44:59,405 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:44:59,406 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 12:44:59,408 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 12:44:59,408 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 12:44:59,408 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 12:44:59,410 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 12:44:59,410 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 12:44:59,411 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 12:44:59,412 - jarvis.core.memory.storage - INFO - Loaded 7 conversations
2025-07-11 12:44:59,413 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 12:44:59,413 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 12:44:59,413 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 12:44:59,413 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 12:44:59,413 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:44:59,414 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:46:12,603 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:46:12,603 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 13:13:06,834 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 13:13:06,835 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 13:13:07,759 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 13:13:07,759 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 13:13:07,760 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 13:13:07,760 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 13:13:07,760 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 13:13:07,772 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 13:13:07,773 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 13:13:07,773 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 13:13:07,773 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 13:13:07,773 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 13:13:07,776 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 13:13:07,784 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 13:13:07,784 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 13:13:07,840 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 13:13:07,840 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 13:13:07,840 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 13:13:07,840 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 13:13:07,924 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 13:13:07,924 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 13:13:07,939 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 13:13:07,939 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 13:13:07,940 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 13:13:07,940 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 13:13:07,941 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 13:13:07,941 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 13:13:07,945 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 13:13:07,948 - jarvis.core.memory.storage - INFO - Loaded 7 conversations
2025-07-11 13:13:07,949 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 13:13:07,950 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 13:13:07,951 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 13:13:07,951 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 13:13:07,951 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 13:13:07,952 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 13:13:32,851 - jarvis.core.ai.engine - INFO - Processed message: show me system usage...
2025-07-11 13:13:49,397 - jarvis.core.ai.engine - INFO - Processed message: hey...
2025-07-11 13:14:16,271 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 13:14:16,271 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 13:22:18,504 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 13:22:18,504 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 13:22:19,024 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 13:22:19,024 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 13:22:19,025 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 13:22:19,025 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 13:22:19,025 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 13:22:19,035 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 13:22:19,035 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 13:22:19,036 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 13:22:19,036 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 13:22:19,036 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 13:22:19,038 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 13:22:19,047 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 13:22:19,047 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 13:22:19,060 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 13:22:19,061 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 13:22:19,553 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 13:22:19,554 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 13:22:19,554 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 13:22:19,554 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 13:22:19,555 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 13:22:19,691 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 13:22:19,691 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 13:22:19,702 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 13:22:19,702 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 13:22:19,703 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 13:22:19,703 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 13:22:19,703 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 13:22:19,704 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 13:22:19,706 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 13:22:19,707 - jarvis.core.memory.storage - INFO - Loaded 9 conversations
2025-07-11 13:22:19,708 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 13:22:19,708 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 13:22:19,708 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 13:22:19,709 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 13:22:19,709 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 13:22:19,709 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 13:23:27,839 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 13:23:27,840 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 13:23:39,435 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 13:23:39,436 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 13:23:40,295 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-4
2025-07-11 13:23:40,295 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 13:23:40,300 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 13:23:40,301 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 13:23:40,301 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 13:23:40,301 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 13:23:40,301 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 13:23:40,304 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 13:23:40,306 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 13:23:40,306 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 13:23:40,319 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 13:23:40,319 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 13:23:40,636 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 13:23:40,637 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 13:23:40,638 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 13:23:40,638 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 13:23:40,639 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 13:23:40,750 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 13:23:40,751 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 13:23:40,766 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 13:23:40,767 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 13:23:40,767 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 13:23:40,768 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 13:23:40,768 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 13:23:40,769 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 13:23:40,773 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 13:23:40,776 - jarvis.core.memory.storage - INFO - Loaded 9 conversations
2025-07-11 13:23:40,778 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 13:23:40,779 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 13:23:40,779 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 13:23:40,780 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 13:23:40,780 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 13:23:40,781 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 13:24:04,369 - jarvis.core.tasks.workflow - INFO - Starting workflow: NLP Workflow: search
2025-07-11 13:24:04,619 - jarvis.core.tasks.executor - INFO - Task completed: cebff481-f434-4eaa-b327-463a349febde - Success: True
2025-07-11 13:24:04,620 - jarvis.core.tasks.executor - INFO - Task completed: e31a45f0-1095-4840-92b5-891ffc32a0bd - Success: False
2025-07-11 13:24:04,620 - jarvis.core.tasks.workflow - INFO - Workflow NLP Workflow: search completed successfully
2025-07-11 13:24:04,620 - jarvis.core.ai.engine - INFO - Task executed successfully: web.search
2025-07-11 13:24:06,002 - jarvis.core.ai.providers - ERROR - OpenAI API error: Error code: 404 - {'error': {'message': 'The model `gpt-4` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-07-11 13:24:06,003 - jarvis.core.ai.engine - ERROR - Error processing message: Error code: 404 - {'error': {'message': 'The model `gpt-4` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-07-11 13:24:49,667 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 13:24:49,671 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 13:37:49,701 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 13:37:49,701 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 13:37:50,587 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-3.5-turbo
2025-07-11 13:37:50,587 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 13:37:50,600 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 13:37:50,600 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 13:37:50,600 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 13:37:50,600 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 13:37:50,600 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 13:37:50,602 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 13:37:50,612 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 13:37:50,613 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 13:37:50,625 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 13:37:50,625 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 13:37:50,959 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 13:37:50,960 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 13:37:50,960 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 13:37:50,961 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 13:37:50,961 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 13:37:51,101 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 13:37:51,102 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 13:37:51,116 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 13:37:51,116 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 13:37:51,141 - jarvis.integrations.voice.speech_recognition - WARNING - Whisper not available. Install with: pip install openai-whisper
2025-07-11 13:37:51,142 - jarvis.integrations.voice.speech_recognition - WARNING - SpeechRecognition not available. Install with: pip install SpeechRecognition
2025-07-11 13:37:51,142 - jarvis.integrations.voice.speech_recognition - WARNING - Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech
2025-07-11 13:37:51,143 - jarvis.integrations.voice.speech_recognition - INFO - Speech recognizer initialized with provider: whisper
2025-07-11 13:37:51,143 - jarvis.integrations.voice.text_to_speech - WARNING - pyttsx3 not available. Install with: pip install pyttsx3
2025-07-11 13:37:51,144 - jarvis.integrations.voice.text_to_speech - WARNING - gTTS not available. Install with: pip install gtts
2025-07-11 13:37:51,144 - jarvis.integrations.voice.text_to_speech - INFO - Text-to-speech initialized with provider: pyttsx3
2025-07-11 13:37:51,145 - jarvis.integrations.voice.voice_interface - INFO - Voice interface initialized
2025-07-11 13:37:51,145 - jarvis.core.tasks.executor - INFO - Registered task handler: voice
2025-07-11 13:37:51,146 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 13:37:51,146 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 13:37:51,147 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 13:37:51,147 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 13:37:51,150 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 13:37:51,151 - jarvis.core.memory.storage - INFO - Loaded 10 conversations
2025-07-11 13:37:51,152 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 13:37:51,152 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 13:37:51,153 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 13:37:51,153 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 13:37:51,153 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 13:37:51,153 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 13:38:20,660 - jarvis.core.ai.providers - ERROR - OpenAI API error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-11 13:38:20,661 - jarvis.core.ai.engine - ERROR - Error processing message: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-11 13:38:39,333 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 13:38:39,333 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 13:48:29,489 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 13:48:29,490 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 13:48:30,176 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-3.5-turbo
2025-07-11 13:48:30,177 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 13:48:30,181 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 13:48:30,181 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 13:48:30,181 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 13:48:30,181 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 13:48:30,181 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 13:48:30,183 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 13:48:30,193 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 13:48:30,193 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 13:48:30,203 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 13:48:30,204 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 13:48:30,519 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 13:48:30,520 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 13:48:30,520 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 13:48:30,521 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 13:48:30,521 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 13:48:30,687 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 13:48:30,687 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 13:48:30,698 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 13:48:30,698 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 13:48:30,701 - jarvis.integrations.voice.speech_recognition - WARNING - Whisper not available. Install with: pip install openai-whisper
2025-07-11 13:48:30,702 - jarvis.integrations.voice.speech_recognition - WARNING - SpeechRecognition not available. Install with: pip install SpeechRecognition
2025-07-11 13:48:30,702 - jarvis.integrations.voice.speech_recognition - WARNING - Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech
2025-07-11 13:48:30,703 - jarvis.integrations.voice.speech_recognition - INFO - Speech recognizer initialized with provider: whisper
2025-07-11 13:48:30,703 - jarvis.integrations.voice.text_to_speech - WARNING - pyttsx3 not available. Install with: pip install pyttsx3
2025-07-11 13:48:30,704 - jarvis.integrations.voice.text_to_speech - WARNING - gTTS not available. Install with: pip install gtts
2025-07-11 13:48:30,704 - jarvis.integrations.voice.text_to_speech - INFO - Text-to-speech initialized with provider: pyttsx3
2025-07-11 13:48:30,705 - jarvis.integrations.voice.voice_interface - INFO - Voice interface initialized
2025-07-11 13:48:30,705 - jarvis.core.tasks.executor - INFO - Registered task handler: voice
2025-07-11 13:48:30,720 - jarvis.core.tasks.handlers - WARNING - GUI integration modules not available
2025-07-11 13:48:30,720 - jarvis.core.tasks.executor - INFO - Registered task handler: gui
2025-07-11 13:48:30,720 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 13:48:30,721 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 13:48:30,721 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 13:48:30,721 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 13:48:30,723 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 13:48:30,724 - jarvis.core.memory.storage - INFO - Loaded 11 conversations
2025-07-11 13:48:30,725 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 13:48:30,726 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 13:48:30,726 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 13:48:30,727 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 13:48:30,727 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 13:48:30,728 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 13:49:04,593 - jarvis.core.ai.providers - ERROR - OpenAI API error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-11 13:49:04,593 - jarvis.core.ai.engine - ERROR - Error processing message: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-11 13:49:40,961 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 13:49:40,963 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 13:58:01,132 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 13:58:01,133 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 13:58:02,146 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-3.5-turbo
2025-07-11 13:58:02,146 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 13:58:02,151 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 13:58:02,151 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 13:58:02,151 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 13:58:02,151 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 13:58:02,151 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 13:58:02,153 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 13:58:02,155 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 13:58:02,155 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 13:58:02,169 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 13:58:02,169 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 13:58:02,476 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 13:58:02,477 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 13:58:02,477 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 13:58:02,478 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 13:58:02,478 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 13:58:02,559 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 13:58:02,560 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 13:58:02,567 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 13:58:02,567 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 13:58:02,570 - jarvis.integrations.voice.speech_recognition - WARNING - Whisper not available. Install with: pip install openai-whisper
2025-07-11 13:58:02,571 - jarvis.integrations.voice.speech_recognition - WARNING - SpeechRecognition not available. Install with: pip install SpeechRecognition
2025-07-11 13:58:02,571 - jarvis.integrations.voice.speech_recognition - WARNING - Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech
2025-07-11 13:58:02,571 - jarvis.integrations.voice.speech_recognition - INFO - Speech recognizer initialized with provider: whisper
2025-07-11 13:58:02,571 - jarvis.integrations.voice.text_to_speech - WARNING - pyttsx3 not available. Install with: pip install pyttsx3
2025-07-11 13:58:02,572 - jarvis.integrations.voice.text_to_speech - WARNING - gTTS not available. Install with: pip install gtts
2025-07-11 13:58:02,572 - jarvis.integrations.voice.text_to_speech - INFO - Text-to-speech initialized with provider: pyttsx3
2025-07-11 13:58:02,572 - jarvis.integrations.voice.voice_interface - INFO - Voice interface initialized
2025-07-11 13:58:02,572 - jarvis.core.tasks.executor - INFO - Registered task handler: voice
2025-07-11 13:58:02,573 - jarvis.core.tasks.handlers - WARNING - GUI integration modules not available
2025-07-11 13:58:02,573 - jarvis.core.tasks.executor - INFO - Registered task handler: gui
2025-07-11 13:58:02,573 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 13:58:02,573 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 13:58:02,574 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 13:58:02,574 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 13:58:02,582 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.config'
2025-07-11 13:58:02,583 - jarvis.core.memory.storage - INFO - Loaded 12 conversations
2025-07-11 13:58:02,583 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 13:58:02,584 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 13:58:02,584 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 13:58:02,584 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 13:58:02,584 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 13:58:02,584 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 13:58:53,394 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 13:58:53,397 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 14:14:16,095 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 14:14:16,095 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 14:14:16,759 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-3.5-turbo
2025-07-11 14:14:16,759 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 14:14:16,763 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 14:14:16,763 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 14:14:16,763 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 14:14:16,763 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 14:14:16,763 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 14:14:16,765 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 14:14:16,766 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 14:14:16,766 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 14:14:16,775 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 14:14:16,776 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 14:14:17,035 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 14:14:17,035 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 14:14:17,035 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 14:14:17,036 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 14:14:17,036 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 14:14:17,217 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 14:14:17,217 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 14:14:17,230 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 14:14:17,231 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 14:14:17,235 - jarvis.integrations.voice.speech_recognition - WARNING - Whisper not available. Install with: pip install openai-whisper
2025-07-11 14:14:17,236 - jarvis.integrations.voice.speech_recognition - WARNING - SpeechRecognition not available. Install with: pip install SpeechRecognition
2025-07-11 14:14:17,236 - jarvis.integrations.voice.speech_recognition - WARNING - Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech
2025-07-11 14:14:17,236 - jarvis.integrations.voice.speech_recognition - INFO - Speech recognizer initialized with provider: whisper
2025-07-11 14:14:17,237 - jarvis.integrations.voice.text_to_speech - WARNING - pyttsx3 not available. Install with: pip install pyttsx3
2025-07-11 14:14:17,237 - jarvis.integrations.voice.text_to_speech - WARNING - gTTS not available. Install with: pip install gtts
2025-07-11 14:14:17,237 - jarvis.integrations.voice.text_to_speech - INFO - Text-to-speech initialized with provider: pyttsx3
2025-07-11 14:14:17,237 - jarvis.integrations.voice.voice_interface - INFO - Voice interface initialized
2025-07-11 14:14:17,238 - jarvis.core.tasks.executor - INFO - Registered task handler: voice
2025-07-11 14:14:17,239 - jarvis.core.tasks.handlers - WARNING - GUI integration modules not available
2025-07-11 14:14:17,239 - jarvis.core.tasks.executor - INFO - Registered task handler: gui
2025-07-11 14:14:17,239 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 14:14:17,239 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 14:14:17,239 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 14:14:17,240 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 14:14:17,242 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.config'
2025-07-11 14:14:17,243 - jarvis.core.memory.storage - INFO - Loaded 12 conversations
2025-07-11 14:14:17,243 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 14:14:17,243 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 14:14:17,244 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 14:14:17,244 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 14:14:17,244 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 14:14:17,244 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 14:14:29,699 - jarvis.core.ai.providers - ERROR - OpenAI API error: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-11 14:14:29,699 - jarvis.core.ai.engine - ERROR - Error processing message: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-07-11 14:14:46,649 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 14:14:46,650 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 14:29:05,248 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 14:29:05,249 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 14:29:06,046 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-3.5-turbo
2025-07-11 14:29:06,047 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 14:29:06,053 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 14:29:06,053 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 14:29:06,054 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 14:29:06,054 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 14:29:06,054 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 14:29:06,057 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 14:29:06,059 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 14:29:06,059 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 14:29:06,075 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 14:29:06,075 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 14:29:06,387 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 14:29:06,388 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 14:29:06,388 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 14:29:06,388 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 14:29:06,388 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 14:29:06,590 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 14:29:06,591 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 14:29:06,605 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 14:29:06,605 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 14:29:06,611 - jarvis.integrations.voice.speech_recognition - WARNING - Whisper not available. Install with: pip install openai-whisper
2025-07-11 14:29:06,612 - jarvis.integrations.voice.speech_recognition - WARNING - SpeechRecognition not available. Install with: pip install SpeechRecognition
2025-07-11 14:29:06,613 - jarvis.integrations.voice.speech_recognition - WARNING - Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech
2025-07-11 14:29:06,613 - jarvis.integrations.voice.speech_recognition - INFO - Speech recognizer initialized with provider: whisper
2025-07-11 14:29:06,614 - jarvis.integrations.voice.text_to_speech - WARNING - pyttsx3 not available. Install with: pip install pyttsx3
2025-07-11 14:29:06,615 - jarvis.integrations.voice.text_to_speech - WARNING - gTTS not available. Install with: pip install gtts
2025-07-11 14:29:06,616 - jarvis.integrations.voice.text_to_speech - INFO - Text-to-speech initialized with provider: pyttsx3
2025-07-11 14:29:06,616 - jarvis.integrations.voice.voice_interface - INFO - Voice interface initialized
2025-07-11 14:29:06,617 - jarvis.core.tasks.executor - INFO - Registered task handler: voice
2025-07-11 14:29:06,620 - jarvis.core.tasks.handlers - WARNING - GUI integration modules not available
2025-07-11 14:29:06,621 - jarvis.core.tasks.executor - INFO - Registered task handler: gui
2025-07-11 14:29:06,621 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 14:29:06,622 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 14:29:06,622 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 14:29:06,622 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 14:29:06,627 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.config'
2025-07-11 14:29:06,629 - jarvis.core.memory.storage - INFO - Loaded 13 conversations
2025-07-11 14:29:06,630 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 14:29:06,631 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 14:29:06,631 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 14:29:06,631 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 14:29:06,632 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 14:29:06,632 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 14:29:50,532 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 14:29:50,533 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 15:58:45,770 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 15:58:45,770 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 15:58:47,277 - jarvis.core.ai.providers - INFO - OpenAI provider initialized with model: gpt-3.5-turbo
2025-07-11 15:58:47,277 - jarvis.core.ai.engine - INFO - Initialized AI provider: openai
2025-07-11 15:58:47,300 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 15:58:47,300 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 15:58:47,301 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 15:58:47,302 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 15:58:47,302 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 15:58:47,311 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 15:58:47,314 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 15:58:47,314 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 15:58:47,345 - jarvis.integrations.system.file_manager - INFO - File manager initialized - Safe mode: True
2025-07-11 15:58:47,345 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 15:58:47,756 - jarvis.integrations.web.browser - INFO - Web browser initialized
2025-07-11 15:58:47,757 - jarvis.integrations.web.search - INFO - Search engine initialized
2025-07-11 15:58:47,757 - jarvis.integrations.web.scraper - INFO - Web scraper initialized
2025-07-11 15:58:47,758 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 15:58:47,758 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 15:58:48,025 - jarvis.integrations.system.app_controller - INFO - Application controller initialized - Safe mode: True
2025-07-11 15:58:48,026 - jarvis.core.tasks.executor - INFO - Registered task handler: app
2025-07-11 15:58:48,040 - jarvis.integrations.system.system_monitor - INFO - System monitor initialized
2025-07-11 15:58:48,041 - jarvis.core.tasks.executor - INFO - Registered task handler: monitor
2025-07-11 15:58:48,060 - jarvis.integrations.voice.speech_recognition - ERROR - Failed to load Whisper model: argument of type 'NoneType' is not iterable
2025-07-11 15:58:48,230 - jarvis.integrations.voice.speech_recognition - INFO - Google Speech Recognition initialized
2025-07-11 15:58:48,663 - jarvis.integrations.voice.speech_recognition - WARNING - Azure Speech API key not configured
2025-07-11 15:58:48,664 - jarvis.integrations.voice.speech_recognition - INFO - Speech recognizer initialized with provider: whisper
2025-07-11 15:58:48,730 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\Downloads\\Jarvis-main\\venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-11 15:58:48,730 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Downloads\Jarvis-main\venv\Lib\site-packages\comtypes\gen'
2025-07-11 15:58:48,978 - jarvis.integrations.voice.text_to_speech - INFO - pyttsx3 TTS engine initialized
2025-07-11 15:58:48,988 - jarvis.integrations.voice.text_to_speech - INFO - Google TTS (gTTS) available
2025-07-11 15:58:48,989 - jarvis.integrations.voice.text_to_speech - INFO - Text-to-speech initialized with provider: pyttsx3
2025-07-11 15:58:48,989 - jarvis.integrations.voice.voice_interface - INFO - Voice interface initialized
2025-07-11 15:58:48,989 - jarvis.core.tasks.executor - INFO - Registered task handler: voice
2025-07-11 15:58:48,992 - jarvis.core.tasks.handlers - WARNING - GUI integration modules not available
2025-07-11 15:58:48,992 - jarvis.core.tasks.executor - INFO - Registered task handler: gui
2025-07-11 15:58:48,992 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 15:58:48,993 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 15:58:48,993 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 15:58:48,993 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 15:58:49,024 - jarvis.core.memory.storage - INFO - Loaded 13 conversations
2025-07-11 15:58:49,024 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 15:58:49,025 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 15:58:49,025 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 15:58:49,025 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 15:58:49,025 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 15:58:49,026 - jarvis.core.context.manager - INFO - Context manager initialized
2025-07-11 15:58:49,026 - jarvis.core.ai.engine - INFO - Context management system initialized
2025-07-11 15:58:49,026 - jarvis.core.memory.storage - INFO - Loaded 13 conversations
2025-07-11 15:58:49,027 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 15:58:49,027 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 15:58:49,027 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 15:58:49,027 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 15:58:49,028 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 15:58:49,028 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
