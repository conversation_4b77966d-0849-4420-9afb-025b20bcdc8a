"""
Base classes for task execution in JARVIS AI Assistant
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
import uuid
import logging

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskResult:
    """Result of task execution"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None


@dataclass
class Task:
    """Base task class"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    command: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[TaskResult] = None
    priority: int = 0  # Higher number = higher priority
    timeout: Optional[float] = None  # Timeout in seconds
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        """Post-initialization setup"""
        if not self.name and self.command:
            self.name = f"Task: {self.command}"
    
    def start(self):
        """Mark task as started"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete(self, result: TaskResult):
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED if result.success else TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.result = result
    
    def cancel(self):
        """Mark task as cancelled"""
        self.status = TaskStatus.CANCELLED
        self.completed_at = datetime.now()
    
    def can_retry(self) -> bool:
        """Check if task can be retried"""
        return (self.status == TaskStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    def retry(self):
        """Prepare task for retry"""
        if self.can_retry():
            self.retry_count += 1
            self.status = TaskStatus.PENDING
            self.started_at = None
            self.completed_at = None
            self.result = None
    
    def get_execution_time(self) -> Optional[float]:
        """Get task execution time in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "command": self.command,
            "parameters": self.parameters,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "priority": self.priority,
            "timeout": self.timeout,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "execution_time": self.get_execution_time(),
            "result": {
                "success": self.result.success,
                "data": self.result.data,
                "error": self.result.error,
                "metadata": self.result.metadata,
                "execution_time": self.result.execution_time
            } if self.result else None
        }


class TaskHandler(ABC):
    """Abstract base class for task handlers"""
    
    @abstractmethod
    async def execute(self, task: Task) -> TaskResult:
        """Execute a task and return the result"""
        pass
    
    @abstractmethod
    def can_handle(self, task: Task) -> bool:
        """Check if this handler can execute the given task"""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get the handler name"""
        pass
    
    @property
    def description(self) -> str:
        """Get the handler description"""
        return f"Handler for {self.name}"
    
    async def validate_task(self, task: Task) -> bool:
        """Validate if the task can be executed"""
        return self.can_handle(task)
    
    async def prepare_task(self, task: Task) -> bool:
        """Prepare the task for execution (optional override)"""
        return True
    
    async def cleanup_task(self, task: Task, result: TaskResult):
        """Cleanup after task execution (optional override)"""
        pass


class SystemTaskHandler(TaskHandler):
    """Handler for system-level tasks"""
    
    @property
    def name(self) -> str:
        return "system"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a system task"""
        return task.command.startswith("system.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a system task"""
        try:
            command = task.command.replace("system.", "")
            
            if command == "info":
                return await self._get_system_info(task)
            elif command == "time":
                return await self._get_current_time(task)
            elif command == "date":
                return await self._get_current_date(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown system command: {command}"
                )
                
        except Exception as e:
            logger.error(f"System task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _get_system_info(self, task: Task) -> TaskResult:
        """Get system information"""
        import platform
        import psutil
        
        info = {
            "platform": platform.platform(),
            "system": platform.system(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "disk_usage": psutil.disk_usage('/').percent if platform.system() != "Windows" else psutil.disk_usage('C:').percent
        }
        
        return TaskResult(success=True, data=info)
    
    async def _get_current_time(self, task: Task) -> TaskResult:
        """Get current time"""
        current_time = datetime.now().strftime("%H:%M:%S")
        return TaskResult(success=True, data=current_time)
    
    async def _get_current_date(self, task: Task) -> TaskResult:
        """Get current date"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        return TaskResult(success=True, data=current_date)
