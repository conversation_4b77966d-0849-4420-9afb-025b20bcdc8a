"""
Advanced File Manager for JARVIS AI Assistant
"""

import os
import shutil
import stat
import hashlib
import mimetypes
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import json

from ...config import config

logger = logging.getLogger(__name__)


class FileManager:
    """Advanced file system operations with security and safety controls"""
    
    def __init__(self):
        """Initialize file manager"""
        self.safe_mode = config.get("system.file_operations.safe_mode", True)
        self.allowed_extensions = set(config.get("system.file_operations.allowed_extensions", [
            ".txt", ".md", ".json", ".yaml", ".yml", ".csv", ".log", ".py", ".js", ".html", ".css"
        ]))
        self.restricted_paths = set(config.get("system.file_operations.restricted_paths", [
            "C:\\Windows", "C:\\Program Files", "/etc", "/usr", "/bin", "/sbin"
        ]))
        
        # Create safe working directory
        self.safe_directory = Path("jarvis_workspace")
        self.safe_directory.mkdir(exist_ok=True)
        
        logger.info(f"File manager initialized - Safe mode: {self.safe_mode}")
    
    def is_safe_path(self, path: Union[str, Path]) -> bool:
        """Check if path is safe to operate on"""
        path = Path(path).resolve()
        
        # Check if path is in restricted directories
        for restricted in self.restricted_paths:
            try:
                if path.is_relative_to(Path(restricted)):
                    return False
            except (ValueError, OSError):
                continue
        
        # In safe mode, only allow operations in safe directory
        if self.safe_mode:
            try:
                path.relative_to(self.safe_directory.resolve())
                return True
            except ValueError:
                return False
        
        return True
    
    def is_allowed_extension(self, path: Union[str, Path]) -> bool:
        """Check if file extension is allowed"""
        if not self.allowed_extensions:
            return True  # No restrictions
        
        extension = Path(path).suffix.lower()
        return extension in self.allowed_extensions
    
    def list_directory(self, path: str = ".", detailed: bool = False) -> Dict[str, Any]:
        """
        List directory contents
        
        Args:
            path: Directory path to list
            detailed: Include detailed file information
            
        Returns:
            Dictionary with directory listing
        """
        try:
            target_path = Path(path).resolve()
            
            if not self.is_safe_path(target_path):
                return {
                    "success": False,
                    "error": f"Access denied to path: {path}",
                    "path": str(target_path)
                }
            
            if not target_path.exists():
                return {
                    "success": False,
                    "error": f"Path does not exist: {path}",
                    "path": str(target_path)
                }
            
            if not target_path.is_dir():
                return {
                    "success": False,
                    "error": f"Path is not a directory: {path}",
                    "path": str(target_path)
                }
            
            items = []
            for item in target_path.iterdir():
                item_info = {
                    "name": item.name,
                    "type": "directory" if item.is_dir() else "file",
                    "path": str(item)
                }
                
                if detailed:
                    try:
                        stat_info = item.stat()
                        # Use st_birthtime for creation time (st_ctime is deprecated)
                        creation_time = getattr(stat_info, 'st_birthtime', stat_info.st_ctime)

                        additional_info = {
                            "size": stat_info.st_size,
                            "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                            "created": datetime.fromtimestamp(creation_time).isoformat(),
                            "permissions": oct(stat_info.st_mode)[-3:],
                            "extension": item.suffix.lower() if item.is_file() else None,
                            "mime_type": mimetypes.guess_type(str(item))[0] if item.is_file() else None
                        }
                        item_info.update(additional_info)
                    except (OSError, PermissionError) as e:
                        item_info["error"] = str(e)
                
                items.append(item_info)
            
            # Sort items: directories first, then files
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            return {
                "success": True,
                "path": str(target_path),
                "items": items,
                "count": len(items),
                "directories": len([i for i in items if i["type"] == "directory"]),
                "files": len([i for i in items if i["type"] == "file"])
            }
            
        except Exception as e:
            logger.error(f"Error listing directory {path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "path": path
            }
    
    def read_file(self, path: str, encoding: str = "utf-8") -> Dict[str, Any]:
        """
        Read file contents
        
        Args:
            path: File path to read
            encoding: Text encoding (default: utf-8)
            
        Returns:
            Dictionary with file contents
        """
        try:
            file_path = Path(path).resolve()
            
            if not self.is_safe_path(file_path):
                return {
                    "success": False,
                    "error": f"Access denied to file: {path}"
                }
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File does not exist: {path}"
                }
            
            if not file_path.is_file():
                return {
                    "success": False,
                    "error": f"Path is not a file: {path}"
                }
            
            # Check file size (limit to 10MB for safety)
            file_size = file_path.stat().st_size
            if file_size > 10 * 1024 * 1024:  # 10MB
                return {
                    "success": False,
                    "error": f"File too large: {file_size} bytes (max 10MB)"
                }
            
            # Try to read as text first
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                
                return {
                    "success": True,
                    "path": str(file_path),
                    "content": content,
                    "size": file_size,
                    "encoding": encoding,
                    "type": "text",
                    "lines": len(content.splitlines()),
                    "mime_type": mimetypes.guess_type(str(file_path))[0]
                }
                
            except UnicodeDecodeError:
                # Try to read as binary
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                return {
                    "success": True,
                    "path": str(file_path),
                    "content": content.hex(),
                    "size": file_size,
                    "type": "binary",
                    "mime_type": mimetypes.guess_type(str(file_path))[0]
                }
                
        except Exception as e:
            logger.error(f"Error reading file {path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "path": path
            }
    
    def write_file(
        self, 
        path: str, 
        content: str, 
        encoding: str = "utf-8",
        create_dirs: bool = True,
        backup: bool = True
    ) -> Dict[str, Any]:
        """
        Write content to file
        
        Args:
            path: File path to write
            content: Content to write
            encoding: Text encoding
            create_dirs: Create parent directories if needed
            backup: Create backup if file exists
            
        Returns:
            Dictionary with operation result
        """
        try:
            file_path = Path(path).resolve()
            
            if not self.is_safe_path(file_path):
                return {
                    "success": False,
                    "error": f"Access denied to file: {path}"
                }
            
            if not self.is_allowed_extension(file_path):
                return {
                    "success": False,
                    "error": f"File extension not allowed: {file_path.suffix}"
                }
            
            # Create parent directories if needed
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create backup if file exists
            backup_path = None
            if backup and file_path.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = file_path.with_suffix(f".backup_{timestamp}{file_path.suffix}")
                shutil.copy2(file_path, backup_path)
            
            # Write content
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            result = {
                "success": True,
                "path": str(file_path),
                "size": len(content.encode(encoding)),
                "encoding": encoding,
                "created_dirs": create_dirs and not file_path.parent.exists(),
                "backup_created": backup_path is not None
            }
            
            if backup_path:
                result["backup_path"] = str(backup_path)
            
            logger.info(f"File written successfully: {file_path}")
            return result
            
        except Exception as e:
            logger.error(f"Error writing file {path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "path": path
            }
    
    def copy_file(self, source: str, destination: str, overwrite: bool = False) -> Dict[str, Any]:
        """Copy file from source to destination"""
        try:
            src_path = Path(source).resolve()
            dst_path = Path(destination).resolve()
            
            if not self.is_safe_path(src_path) or not self.is_safe_path(dst_path):
                return {
                    "success": False,
                    "error": "Access denied to source or destination path"
                }
            
            if not src_path.exists():
                return {
                    "success": False,
                    "error": f"Source file does not exist: {source}"
                }
            
            if dst_path.exists() and not overwrite:
                return {
                    "success": False,
                    "error": f"Destination file exists and overwrite is disabled: {destination}"
                }
            
            # Create destination directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            shutil.copy2(src_path, dst_path)
            
            return {
                "success": True,
                "source": str(src_path),
                "destination": str(dst_path),
                "size": dst_path.stat().st_size
            }
            
        except Exception as e:
            logger.error(f"Error copying file {source} to {destination}: {e}")
            return {
                "success": False,
                "error": str(e),
                "source": source,
                "destination": destination
            }
    
    def move_file(self, source: str, destination: str, overwrite: bool = False) -> Dict[str, Any]:
        """Move file from source to destination"""
        try:
            src_path = Path(source).resolve()
            dst_path = Path(destination).resolve()
            
            if not self.is_safe_path(src_path) or not self.is_safe_path(dst_path):
                return {
                    "success": False,
                    "error": "Access denied to source or destination path"
                }
            
            if not src_path.exists():
                return {
                    "success": False,
                    "error": f"Source file does not exist: {source}"
                }
            
            if dst_path.exists() and not overwrite:
                return {
                    "success": False,
                    "error": f"Destination file exists and overwrite is disabled: {destination}"
                }
            
            # Create destination directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(str(src_path), str(dst_path))
            
            return {
                "success": True,
                "source": str(src_path),
                "destination": str(dst_path)
            }
            
        except Exception as e:
            logger.error(f"Error moving file {source} to {destination}: {e}")
            return {
                "success": False,
                "error": str(e),
                "source": source,
                "destination": destination
            }
    
    def delete_file(self, path: str, permanent: bool = False) -> Dict[str, Any]:
        """
        Delete file (with optional trash/recycle bin support)
        
        Args:
            path: File path to delete
            permanent: If True, delete permanently; if False, move to trash
            
        Returns:
            Dictionary with operation result
        """
        try:
            file_path = Path(path).resolve()
            
            if not self.is_safe_path(file_path):
                return {
                    "success": False,
                    "error": f"Access denied to file: {path}"
                }
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File does not exist: {path}"
                }
            
            if permanent:
                # Permanent deletion
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
                
                return {
                    "success": True,
                    "path": str(file_path),
                    "permanent": True
                }
            else:
                # Move to trash (create a trash directory in safe workspace)
                trash_dir = self.safe_directory / ".trash"
                trash_dir.mkdir(exist_ok=True)
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                trash_path = trash_dir / f"{file_path.name}_{timestamp}"
                
                shutil.move(str(file_path), str(trash_path))
                
                return {
                    "success": True,
                    "path": str(file_path),
                    "trash_path": str(trash_path),
                    "permanent": False
                }
                
        except Exception as e:
            logger.error(f"Error deleting file {path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "path": path
            }
    
    def create_directory(self, path: str, parents: bool = True) -> Dict[str, Any]:
        """Create directory"""
        try:
            dir_path = Path(path).resolve()
            
            if not self.is_safe_path(dir_path):
                return {
                    "success": False,
                    "error": f"Access denied to path: {path}"
                }
            
            if dir_path.exists():
                return {
                    "success": False,
                    "error": f"Directory already exists: {path}"
                }
            
            dir_path.mkdir(parents=parents, exist_ok=False)
            
            return {
                "success": True,
                "path": str(dir_path),
                "parents_created": parents
            }
            
        except Exception as e:
            logger.error(f"Error creating directory {path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "path": path
            }
    
    def get_file_info(self, path: str) -> Dict[str, Any]:
        """Get detailed file information"""
        try:
            file_path = Path(path).resolve()
            
            if not self.is_safe_path(file_path):
                return {
                    "success": False,
                    "error": f"Access denied to file: {path}"
                }
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File does not exist: {path}"
                }
            
            stat_info = file_path.stat()
            
            # Use st_birthtime for creation time (st_ctime is deprecated)
            creation_time = getattr(stat_info, 'st_birthtime', stat_info.st_ctime)

            info = {
                "success": True,
                "path": str(file_path),
                "name": file_path.name,
                "type": "directory" if file_path.is_dir() else "file",
                "size": stat_info.st_size,
                "created": datetime.fromtimestamp(creation_time).isoformat(),
                "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(stat_info.st_atime).isoformat(),
                "permissions": oct(stat_info.st_mode)[-3:],
                "owner_readable": bool(stat_info.st_mode & stat.S_IRUSR),
                "owner_writable": bool(stat_info.st_mode & stat.S_IWUSR),
                "owner_executable": bool(stat_info.st_mode & stat.S_IXUSR)
            }
            
            if file_path.is_file():
                info.update({
                    "extension": file_path.suffix.lower(),
                    "mime_type": mimetypes.guess_type(str(file_path))[0],
                    "size_human": self._format_size(stat_info.st_size)
                })
                
                # Calculate file hash for integrity checking
                if stat_info.st_size < 100 * 1024 * 1024:  # Only for files < 100MB
                    try:
                        with open(file_path, 'rb') as f:
                            content = f.read()
                            info["md5_hash"] = hashlib.md5(content).hexdigest()
                            info["sha256_hash"] = hashlib.sha256(content).hexdigest()
                    except Exception:
                        pass
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting file info for {path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "path": path
            }
    
    def search_files(
        self, 
        directory: str = ".", 
        pattern: str = "*",
        recursive: bool = True,
        include_content: bool = False,
        max_results: int = 100
    ) -> Dict[str, Any]:
        """Search for files matching pattern"""
        try:
            search_path = Path(directory).resolve()
            
            if not self.is_safe_path(search_path):
                return {
                    "success": False,
                    "error": f"Access denied to directory: {directory}"
                }
            
            if not search_path.exists() or not search_path.is_dir():
                return {
                    "success": False,
                    "error": f"Directory does not exist: {directory}"
                }
            
            results = []
            search_method = search_path.rglob if recursive else search_path.glob
            
            for file_path in search_method(pattern):
                if len(results) >= max_results:
                    break
                
                if not self.is_safe_path(file_path):
                    continue
                
                result: Dict[str, Any] = {
                    "path": str(file_path),
                    "name": file_path.name,
                    "type": "directory" if file_path.is_dir() else "file",
                    "relative_path": str(file_path.relative_to(search_path))
                }
                
                if file_path.is_file():
                    stat_info = file_path.stat()
                    file_info = {
                        "size": stat_info.st_size,
                        "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                        "extension": file_path.suffix.lower()
                    }
                    result.update(file_info)
                    
                    # Search file content if requested
                    if include_content and file_path.suffix.lower() in ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.yaml', '.yml']:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                if pattern.replace('*', '') in content:
                                    result["content_match"] = "true"
                                    # Find matching lines
                                    matching_lines = [
                                        (i+1, line.strip()) 
                                        for i, line in enumerate(content.splitlines()) 
                                        if pattern.replace('*', '') in line
                                    ]
                                    result["matching_lines"] = [
                                        {"line_number": line_num, "content": content}
                                        for line_num, content in matching_lines[:5]
                                    ]  # First 5 matches
                        except Exception:
                            pass
                
                results.append(result)
            
            return {
                "success": True,
                "directory": str(search_path),
                "pattern": pattern,
                "recursive": recursive,
                "results": results,
                "count": len(results),
                "truncated": len(results) >= max_results
            }
            
        except Exception as e:
            logger.error(f"Error searching files in {directory}: {e}")
            return {
                "success": False,
                "error": str(e),
                "directory": directory
            }
    
    def _format_size(self, size_bytes: Union[int, float]) -> str:
        """Format file size in human readable format"""
        size_float = float(size_bytes)
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_float < 1024.0:
                return f"{size_float:.1f} {unit}"
            size_float /= 1024.0
        return f"{size_float:.1f} PB"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get file manager statistics"""
        return {
            "safe_mode": self.safe_mode,
            "safe_directory": str(self.safe_directory),
            "allowed_extensions": list(self.allowed_extensions),
            "restricted_paths": list(self.restricted_paths),
            "safe_directory_exists": self.safe_directory.exists()
        }
