"""
Session Management for JARVIS AI Assistant
"""

import uuid
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json
from pathlib import Path

from ...config import config

logger = logging.getLogger(__name__)


@dataclass
class UserSession:
    """User session information"""
    session_id: str
    user_id: Optional[str] = None
    username: Optional[str] = None
    start_time: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    interface_type: str = "cli"  # cli, web, gui, voice
    device_info: Dict[str, Any] = field(default_factory=dict)
    session_data: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)
    is_authenticated: bool = False
    permissions: List[str] = field(default_factory=list)


class SessionManager:
    """Manages user sessions and authentication"""
    
    def __init__(self):
        """Initialize session manager"""
        self.active_sessions: Dict[str, UserSession] = {}
        self.session_timeout = timedelta(hours=config.get("session.timeout_hours", 24))
        self.max_sessions_per_user = config.get("session.max_per_user", 5)
        
        # Session storage
        self.sessions_file = Path(config.get("data_dir", "data")) / "sessions.json"
        self.sessions_file.parent.mkdir(exist_ok=True)
        
        # Load existing sessions
        self._load_sessions()
        
        logger.info("Session manager initialized")
    
    def create_session(
        self, 
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        interface_type: str = "cli",
        device_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new user session"""
        session_id = str(uuid.uuid4())
        
        # Clean up old sessions for this user
        if user_id:
            self._cleanup_user_sessions(user_id)
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            username=username,
            interface_type=interface_type,
            device_info=device_info or {},
            permissions=self._get_default_permissions()
        )
        
        self.active_sessions[session_id] = session
        self._save_sessions()
        
        logger.info(f"Created session {session_id} for user {user_id or 'anonymous'}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[UserSession]:
        """Get session by ID"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # Check if session is expired
        if datetime.now() - session.last_activity > self.session_timeout:
            self.end_session(session_id)
            return None
        
        # Update last activity
        session.last_activity = datetime.now()
        return session
    
    def authenticate_session(
        self, 
        session_id: str, 
        credentials: Dict[str, Any]
    ) -> bool:
        """Authenticate a session"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        # Simple authentication (in production, use proper auth)
        username = credentials.get("username")
        password = credentials.get("password")

        # Validate credentials are not None
        if username is None or password is None:
            return False

        if self._verify_credentials(username, password):
            session.is_authenticated = True
            session.user_id = username
            session.username = username
            session.permissions = self._get_user_permissions(username)
            
            self._save_sessions()
            logger.info(f"Authenticated session {session_id} for user {username}")
            return True
        
        return False
    
    def update_session_data(self, session_id: str, key: str, value: Any):
        """Update session data"""
        session = self.get_session(session_id)
        if session:
            session.session_data[key] = value
            self._save_sessions()
    
    def get_session_data(self, session_id: str, key: str, default: Any = None) -> Any:
        """Get session data"""
        session = self.get_session(session_id)
        if session:
            return session.session_data.get(key, default)
        return default
    
    def set_user_preference(self, session_id: str, key: str, value: Any):
        """Set user preference"""
        session = self.get_session(session_id)
        if session:
            session.preferences[key] = value
            self._save_sessions()
    
    def get_user_preference(self, session_id: str, key: str, default: Any = None) -> Any:
        """Get user preference"""
        session = self.get_session(session_id)
        if session:
            return session.preferences.get(key, default)
        return default
    
    def has_permission(self, session_id: str, permission: str) -> bool:
        """Check if session has permission"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        return permission in session.permissions or "admin" in session.permissions
    
    def end_session(self, session_id: str):
        """End a session"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            del self.active_sessions[session_id]
            self._save_sessions()
            
            logger.info(f"Ended session {session_id} for user {session.user_id or 'anonymous'}")
    
    def get_user_sessions(self, user_id: str) -> List[UserSession]:
        """Get all active sessions for a user"""
        return [
            session for session in self.active_sessions.values()
            if session.user_id == user_id
        ]
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics"""
        # Count sessions by interface type
        interface_counts = {}
        authenticated_count = 0
        
        for session in self.active_sessions.values():
            interface_type = session.interface_type
            interface_counts[interface_type] = interface_counts.get(interface_type, 0) + 1
            
            if session.is_authenticated:
                authenticated_count += 1
        
        return {
            "total_sessions": len(self.active_sessions),
            "authenticated_sessions": authenticated_count,
            "anonymous_sessions": len(self.active_sessions) - authenticated_count,
            "interface_breakdown": interface_counts,
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600
        }
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        now = datetime.now()
        expired_sessions = []

        for session_id, session in self.active_sessions.items():
            if now - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            self.end_session(session_id)

        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def _cleanup_user_sessions(self, user_id: str):
        """Clean up old sessions for a user"""
        user_sessions = self.get_user_sessions(user_id)
        
        if len(user_sessions) >= self.max_sessions_per_user:
            # Sort by last activity and remove oldest
            user_sessions.sort(key=lambda s: s.last_activity)
            sessions_to_remove = user_sessions[:-self.max_sessions_per_user + 1]
            
            for session in sessions_to_remove:
                self.end_session(session.session_id)
    
    def _verify_credentials(self, username: str, password: str) -> bool:
        """Verify user credentials (simplified)"""
        # In production, use proper password hashing and user database
        default_users = config.get("auth.users", {})
        
        if username in default_users:
            return default_users[username] == password
        
        # For development, allow any username/password combination
        if config.get("auth.dev_mode", True):
            return True
        
        return False
    
    def _get_default_permissions(self) -> List[str]:
        """Get default permissions for new sessions"""
        return config.get("auth.default_permissions", [
            "basic_commands",
            "file_read",
            "web_search",
            "system_info"
        ])
    
    def _get_user_permissions(self, username: str) -> List[str]:
        """Get permissions for a specific user"""
        user_permissions = config.get("auth.user_permissions", {})
        
        if username in user_permissions:
            return user_permissions[username]
        
        # Default permissions
        return self._get_default_permissions()
    
    def _load_sessions(self):
        """Load sessions from file"""
        try:
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r') as f:
                    session_data = json.load(f)
                
                for session_id, data in session_data.items():
                    # Convert datetime strings back to datetime objects
                    data['start_time'] = datetime.fromisoformat(data['start_time'])
                    data['last_activity'] = datetime.fromisoformat(data['last_activity'])
                    
                    session = UserSession(**data)
                    self.active_sessions[session_id] = session
                
                logger.info(f"Loaded {len(self.active_sessions)} sessions")
                
        except Exception as e:
            logger.error(f"Error loading sessions: {e}")
    
    def _save_sessions(self):
        """Save sessions to file"""
        try:
            session_data = {}
            
            for session_id, session in self.active_sessions.items():
                data = {
                    'session_id': session.session_id,
                    'user_id': session.user_id,
                    'username': session.username,
                    'start_time': session.start_time.isoformat(),
                    'last_activity': session.last_activity.isoformat(),
                    'interface_type': session.interface_type,
                    'device_info': session.device_info,
                    'session_data': session.session_data,
                    'preferences': session.preferences,
                    'is_authenticated': session.is_authenticated,
                    'permissions': session.permissions
                }
                session_data[session_id] = data
            
            with open(self.sessions_file, 'w') as f:
                json.dump(session_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving sessions: {e}")
