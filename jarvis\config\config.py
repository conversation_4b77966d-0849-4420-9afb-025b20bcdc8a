"""
Configuration management for JARVIS AI Assistant
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)


class Config:
    """Configuration manager for JARVIS AI Assistant"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_path: Path to configuration file (defaults to config/config.yaml)
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config_data: Dict[str, Any] = {}
        self._load_environment()
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path"""
        base_dir = Path(__file__).parent.parent.parent
        return str(base_dir / "jarvis" / "config" / "config.yaml")
    
    def _load_environment(self):
        """Load environment variables from .env file"""
        env_path = Path(__file__).parent.parent.parent / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            logger.info(f"Loaded environment variables from {env_path}")
    
    def _load_config(self):
        """Load configuration from YAML file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    self.config_data = yaml.safe_load(file) or {}
                logger.info(f"Loaded configuration from {self.config_path}")
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                self.config_data = self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self.config_data = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values"""
        return {
            "ai": {
                "provider": "openai",
                "openai": {
                    "model": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            },
            "voice": {
                "recognition": {
                    "provider": "whisper",
                    "language": "en-US"
                },
                "synthesis": {
                    "provider": "pyttsx3",
                    "rate": 200,
                    "volume": 0.9
                }
            },
            "system": {
                "file_operations": {
                    "enabled": True,
                    "safe_mode": True
                },
                "app_control": {
                    "enabled": True,
                    "safe_mode": True
                }
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/jarvis.log"
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation
        
        Args:
            key: Configuration key (e.g., 'ai.openai.model')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        # Check for environment variable override
        env_key = key.upper().replace('.', '_')
        env_value = os.getenv(env_key)
        if env_value is not None:
            return env_value
            
        return value
    
    def set(self, key: str, value: Any):
        """
        Set configuration value using dot notation
        
        Args:
            key: Configuration key (e.g., 'ai.openai.model')
            value: Value to set
        """
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config_data, file, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def reload(self):
        """Reload configuration from file"""
        self._load_config()
    
    def get_api_key(self, service: str) -> Optional[str]:
        """
        Get API key for a service

        Args:
            service: Service name (e.g., 'openai', 'elevenlabs')

        Returns:
            API key or None if not found
        """
        # Try environment variable first
        env_key = f"{service.upper()}_API_KEY"
        api_key = os.getenv(env_key)

        if api_key:
            return api_key

        # Try configuration file - check multiple possible locations
        config_keys = [
            f"ai.{service}.api_key",  # ai.openai.api_key
            f"{service}.api_key",     # openai.api_key
            f"api_keys.{service}"     # api_keys.openai
        ]

        for config_key in config_keys:
            api_key = self.get(config_key)
            if api_key:
                return api_key

        return None
    
    def is_feature_enabled(self, feature: str) -> bool:
        """
        Check if a feature is enabled
        
        Args:
            feature: Feature name (e.g., 'system.file_operations')
            
        Returns:
            True if feature is enabled, False otherwise
        """
        return self.get(f"{feature}.enabled", False)
    
    def get_safe_mode(self, feature: str) -> bool:
        """
        Check if safe mode is enabled for a feature
        
        Args:
            feature: Feature name (e.g., 'system.file_operations')
            
        Returns:
            True if safe mode is enabled, False otherwise
        """
        return self.get(f"{feature}.safe_mode", True)


# Global configuration instance
config = Config()
