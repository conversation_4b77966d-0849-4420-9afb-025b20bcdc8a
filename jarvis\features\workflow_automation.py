"""
Workflow Automation for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json
from pathlib import Path
import asyncio
import uuid

from ..config import config

logger = logging.getLogger(__name__)


@dataclass
class WorkflowStep:
    """Individual workflow step"""
    step_id: str
    name: str
    action: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    conditions: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 30
    on_success: Optional[str] = None  # Next step ID
    on_failure: Optional[str] = None  # Failure step ID


@dataclass
class Workflow:
    """Complete workflow definition"""
    workflow_id: str
    name: str
    description: str
    steps: List[WorkflowStep] = field(default_factory=list)
    triggers: List[str] = field(default_factory=list)
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    last_executed: Optional[datetime] = None
    execution_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WorkflowExecution:
    """Workflow execution instance"""
    execution_id: str
    workflow_id: str
    status: str  # running, completed, failed, cancelled
    started_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    current_step: Optional[str] = None
    step_results: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)


class WorkflowAutomation:
    """Advanced workflow automation system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize workflow automation"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.workflows_file = self.data_dir / "workflows.json"
        self.executions_file = self.data_dir / "workflow_executions.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Storage
        self.workflows: Dict[str, Workflow] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.active_executions: Dict[str, WorkflowExecution] = {}
        
        # Action handlers
        self.action_handlers: Dict[str, Callable] = {}
        
        # Load existing data
        self._load_workflows()
        self._load_executions()
        
        # Register default actions
        self._register_default_actions()
        
        logger.info("Workflow automation initialized")
    
    def create_workflow(
        self,
        name: str,
        description: str,
        steps: List[Dict[str, Any]],
        triggers: Optional[List[str]] = None
    ) -> str:
        """Create a new workflow"""
        workflow_id = str(uuid.uuid4())
        
        # Convert step dictionaries to WorkflowStep objects
        workflow_steps = []
        for i, step_data in enumerate(steps):
            step = WorkflowStep(
                step_id=step_data.get("step_id", f"step_{i}"),
                name=step_data.get("name", f"Step {i+1}"),
                action=step_data["action"],
                parameters=step_data.get("parameters", {}),
                conditions=step_data.get("conditions", {}),
                max_retries=step_data.get("max_retries", 3),
                timeout_seconds=step_data.get("timeout_seconds", 30),
                on_success=step_data.get("on_success"),
                on_failure=step_data.get("on_failure")
            )
            workflow_steps.append(step)
        
        workflow = Workflow(
            workflow_id=workflow_id,
            name=name,
            description=description,
            steps=workflow_steps,
            triggers=triggers or []
        )
        
        self.workflows[workflow_id] = workflow
        self._save_workflows()
        
        logger.info(f"Created workflow: {name}")
        return workflow_id
    
    async def execute_workflow(
        self,
        workflow_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Execute a workflow"""
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        workflow = self.workflows[workflow_id]
        if not workflow.is_active:
            raise ValueError(f"Workflow is not active: {workflow_id}")
        
        # Create execution instance
        execution_id = str(uuid.uuid4())
        execution = WorkflowExecution(
            execution_id=execution_id,
            workflow_id=workflow_id,
            status="running",
            context=context or {}
        )
        
        self.executions[execution_id] = execution
        self.active_executions[execution_id] = execution
        
        # Update workflow stats
        workflow.last_executed = datetime.now()
        workflow.execution_count += 1
        
        logger.info(f"Starting workflow execution: {workflow.name}")
        
        # Execute workflow steps
        try:
            await self._execute_workflow_steps(execution, workflow)
            execution.status = "completed"
            execution.completed_at = datetime.now()
            
        except Exception as e:
            execution.status = "failed"
            execution.error_message = str(e)
            execution.completed_at = datetime.now()
            logger.error(f"Workflow execution failed: {e}")
        
        finally:
            # Remove from active executions
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]
            
            self._save_executions()
            self._save_workflows()
        
        return execution_id
    
    async def _execute_workflow_steps(self, execution: WorkflowExecution, workflow: Workflow):
        """Execute workflow steps"""
        if not workflow.steps:
            return
        
        current_step_index = 0
        
        while current_step_index < len(workflow.steps):
            step = workflow.steps[current_step_index]
            execution.current_step = step.step_id
            
            logger.info(f"Executing step: {step.name}")
            
            try:
                # Check step conditions
                if not self._check_step_conditions(step, execution.context):
                    logger.info(f"Step conditions not met, skipping: {step.name}")
                    current_step_index += 1
                    continue
                
                # Execute step action
                result = await self._execute_step_action(step, execution.context)
                execution.step_results[step.step_id] = result
                
                # Determine next step
                if step.on_success:
                    # Find step by ID
                    next_step_index = self._find_step_index(workflow.steps, step.on_success)
                    if next_step_index is not None:
                        current_step_index = next_step_index
                    else:
                        current_step_index += 1
                else:
                    current_step_index += 1
                
            except Exception as e:
                logger.error(f"Step execution failed: {step.name} - {e}")
                
                # Handle step failure
                if step.retry_count < step.max_retries:
                    step.retry_count += 1
                    logger.info(f"Retrying step: {step.name} (attempt {step.retry_count})")
                    continue
                
                # Max retries reached
                if step.on_failure:
                    next_step_index = self._find_step_index(workflow.steps, step.on_failure)
                    if next_step_index is not None:
                        current_step_index = next_step_index
                        continue
                
                # No failure handler, stop execution
                raise e
    
    async def _execute_step_action(self, step: WorkflowStep, context: Dict[str, Any]) -> Any:
        """Execute a single step action"""
        action = step.action
        parameters = step.parameters.copy()
        
        # Substitute context variables in parameters
        parameters = self._substitute_context_variables(parameters, context)
        
        if action in self.action_handlers:
            handler = self.action_handlers[action]
            
            # Execute with timeout
            try:
                result = await asyncio.wait_for(
                    handler(parameters, context),
                    timeout=step.timeout_seconds
                )
                return result
            except asyncio.TimeoutError:
                raise Exception(f"Step timed out after {step.timeout_seconds} seconds")
        else:
            raise Exception(f"Unknown action: {action}")
    
    def _check_step_conditions(self, step: WorkflowStep, context: Dict[str, Any]) -> bool:
        """Check if step conditions are met"""
        conditions = step.conditions
        
        if not conditions:
            return True
        
        # Simple condition checking
        for key, expected_value in conditions.items():
            if key not in context:
                return False
            
            actual_value = context[key]
            
            # Support different condition types
            if isinstance(expected_value, dict):
                operator = expected_value.get("operator", "equals")
                value = expected_value.get("value")
                
                if operator == "equals" and actual_value != value:
                    return False
                elif operator == "not_equals" and actual_value == value:
                    return False
                elif operator == "greater_than" and actual_value <= value:
                    return False
                elif operator == "less_than" and actual_value >= value:
                    return False
            else:
                if actual_value != expected_value:
                    return False
        
        return True
    
    def _substitute_context_variables(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Substitute context variables in parameters"""
        result = {}
        
        for key, value in parameters.items():
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                # Extract variable name
                var_name = value[2:-1]
                if var_name in context:
                    result[key] = context[var_name]
                else:
                    result[key] = value  # Keep original if not found
            else:
                result[key] = value
        
        return result
    
    def _find_step_index(self, steps: List[WorkflowStep], step_id: str) -> Optional[int]:
        """Find step index by ID"""
        for i, step in enumerate(steps):
            if step.step_id == step_id:
                return i
        return None
    
    def register_action_handler(self, action_name: str, handler: Callable):
        """Register custom action handler"""
        self.action_handlers[action_name] = handler
        logger.info(f"Registered action handler: {action_name}")
    
    def _register_default_actions(self):
        """Register default action handlers"""
        
        async def delay_action(parameters: Dict[str, Any], context: Dict[str, Any]) -> str:
            """Delay/wait action"""
            seconds = parameters.get("seconds", 1)
            await asyncio.sleep(seconds)
            return f"Delayed for {seconds} seconds"
        
        async def log_action(parameters: Dict[str, Any], context: Dict[str, Any]) -> str:
            """Log message action"""
            message = parameters.get("message", "Log message")
            level = parameters.get("level", "info")
            
            if level == "info":
                logger.info(message)
            elif level == "warning":
                logger.warning(message)
            elif level == "error":
                logger.error(message)
            
            return f"Logged: {message}"
        
        async def set_variable_action(parameters: Dict[str, Any], context: Dict[str, Any]) -> str:
            """Set context variable action"""
            variable_name = parameters.get("name")
            variable_value = parameters.get("value")
            
            if variable_name:
                context[variable_name] = variable_value
                return f"Set {variable_name} = {variable_value}"
            
            return "No variable name specified"
        
        # Register default actions
        self.register_action_handler("delay", delay_action)
        self.register_action_handler("log", log_action)
        self.register_action_handler("set_variable", set_variable_action)
    
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status"""
        if workflow_id not in self.workflows:
            return {"error": "Workflow not found"}
        
        workflow = self.workflows[workflow_id]
        
        # Get recent executions
        recent_executions = [
            {
                "execution_id": exec.execution_id,
                "status": exec.status,
                "started_at": exec.started_at.isoformat(),
                "completed_at": exec.completed_at.isoformat() if exec.completed_at else None
            }
            for exec in self.executions.values()
            if exec.workflow_id == workflow_id
        ]
        
        # Sort by start time (most recent first)
        recent_executions.sort(key=lambda x: x["started_at"], reverse=True)
        
        return {
            "workflow_id": workflow.workflow_id,
            "name": workflow.name,
            "description": workflow.description,
            "is_active": workflow.is_active,
            "step_count": len(workflow.steps),
            "execution_count": workflow.execution_count,
            "last_executed": workflow.last_executed.isoformat() if workflow.last_executed else None,
            "recent_executions": recent_executions[:10]  # Last 10 executions
        }
    
    def list_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows"""
        return [
            {
                "workflow_id": workflow.workflow_id,
                "name": workflow.name,
                "description": workflow.description,
                "is_active": workflow.is_active,
                "step_count": len(workflow.steps),
                "execution_count": workflow.execution_count
            }
            for workflow in self.workflows.values()
        ]
    
    def _load_workflows(self):
        """Load workflows from file"""
        try:
            if self.workflows_file.exists():
                with open(self.workflows_file, 'r') as f:
                    workflows_data = json.load(f)
                
                for workflow_id, workflow_dict in workflows_data.items():
                    # Convert datetime strings back to datetime objects
                    workflow_dict["created_at"] = datetime.fromisoformat(workflow_dict["created_at"])
                    if workflow_dict.get("last_executed"):
                        workflow_dict["last_executed"] = datetime.fromisoformat(workflow_dict["last_executed"])
                    
                    # Convert steps
                    steps = []
                    for step_dict in workflow_dict["steps"]:
                        step = WorkflowStep(**step_dict)
                        steps.append(step)
                    workflow_dict["steps"] = steps
                    
                    workflow = Workflow(**workflow_dict)
                    self.workflows[workflow_id] = workflow
                
                logger.info(f"Loaded {len(self.workflows)} workflows")
                
        except Exception as e:
            logger.error(f"Error loading workflows: {e}")
    
    def _save_workflows(self):
        """Save workflows to file"""
        try:
            workflows_data = {}
            
            for workflow_id, workflow in self.workflows.items():
                workflow_dict = {
                    "workflow_id": workflow.workflow_id,
                    "name": workflow.name,
                    "description": workflow.description,
                    "steps": [
                        {
                            "step_id": step.step_id,
                            "name": step.name,
                            "action": step.action,
                            "parameters": step.parameters,
                            "conditions": step.conditions,
                            "retry_count": step.retry_count,
                            "max_retries": step.max_retries,
                            "timeout_seconds": step.timeout_seconds,
                            "on_success": step.on_success,
                            "on_failure": step.on_failure
                        }
                        for step in workflow.steps
                    ],
                    "triggers": workflow.triggers,
                    "is_active": workflow.is_active,
                    "created_at": workflow.created_at.isoformat(),
                    "last_executed": workflow.last_executed.isoformat() if workflow.last_executed else None,
                    "execution_count": workflow.execution_count,
                    "metadata": workflow.metadata
                }
                workflows_data[workflow_id] = workflow_dict
            
            with open(self.workflows_file, 'w') as f:
                json.dump(workflows_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving workflows: {e}")
    
    def _load_executions(self):
        """Load executions from file"""
        try:
            if self.executions_file.exists():
                with open(self.executions_file, 'r') as f:
                    executions_data = json.load(f)
                
                for execution_id, execution_dict in executions_data.items():
                    # Convert datetime strings back to datetime objects
                    execution_dict["started_at"] = datetime.fromisoformat(execution_dict["started_at"])
                    if execution_dict.get("completed_at"):
                        execution_dict["completed_at"] = datetime.fromisoformat(execution_dict["completed_at"])
                    
                    execution = WorkflowExecution(**execution_dict)
                    self.executions[execution_id] = execution
                
                logger.info(f"Loaded {len(self.executions)} workflow executions")
                
        except Exception as e:
            logger.error(f"Error loading executions: {e}")
    
    def _save_executions(self):
        """Save executions to file"""
        try:
            executions_data = {}
            
            for execution_id, execution in self.executions.items():
                execution_dict = {
                    "execution_id": execution.execution_id,
                    "workflow_id": execution.workflow_id,
                    "status": execution.status,
                    "started_at": execution.started_at.isoformat(),
                    "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
                    "current_step": execution.current_step,
                    "step_results": execution.step_results,
                    "error_message": execution.error_message,
                    "context": execution.context
                }
                executions_data[execution_id] = execution_dict
            
            with open(self.executions_file, 'w') as f:
                json.dump(executions_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving executions: {e}")
