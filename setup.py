#!/usr/bin/env python3
"""
Setup script for JARVIS AI Assistant
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True


def create_virtual_environment():
    """Create virtual environment"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("ℹ️  Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")


def install_dependencies():
    """Install required dependencies"""
    if os.name == 'nt':  # Windows
        pip_command = r".\venv\Scripts\pip.exe install -r requirements.txt"
    else:  # Unix/Linux/macOS
        pip_command = "./venv/bin/pip install -r requirements.txt"
    
    return run_command(pip_command, "Installing dependencies")


def create_config_file():
    """Create configuration file from example"""
    config_path = Path("jarvis/config/config.yaml")
    example_path = Path("jarvis/config/config.example.yaml")
    
    if config_path.exists():
        print("ℹ️  Configuration file already exists")
        return True
    
    if not example_path.exists():
        print("❌ Example configuration file not found")
        return False
    
    try:
        import shutil
        shutil.copy(example_path, config_path)
        print("✅ Configuration file created from example")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration file: {e}")
        return False


def create_env_file():
    """Create .env file from example"""
    env_path = Path(".env")
    example_path = Path(".env.example")
    
    if env_path.exists():
        print("ℹ️  Environment file already exists")
        return True
    
    if not example_path.exists():
        print("❌ Example environment file not found")
        return False
    
    try:
        import shutil
        shutil.copy(example_path, env_path)
        print("✅ Environment file created from example")
        return True
    except Exception as e:
        print(f"❌ Failed to create environment file: {e}")
        return False


def create_directories():
    """Create necessary directories"""
    directories = ["logs", "data"]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ Created directory: {directory}")
            except Exception as e:
                print(f"❌ Failed to create directory {directory}: {e}")
                return False
        else:
            print(f"ℹ️  Directory already exists: {directory}")
    
    return True


def test_installation():
    """Test the installation"""
    print("🧪 Testing installation...")
    
    if os.name == 'nt':  # Windows
        test_command = r".\venv\Scripts\python.exe -m jarvis --help"
    else:  # Unix/Linux/macOS
        test_command = "./venv/bin/python -m jarvis --help"
    
    return run_command(test_command, "Testing JARVIS installation")


def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 JARVIS AI Assistant Setup Complete!")
    print("="*60)
    print("\n📋 Next Steps:")
    print("\n1. 🔑 Configure API Keys:")
    print("   - Edit jarvis/config/config.yaml")
    print("   - Add your OpenAI API key (or other AI provider keys)")
    print("   - Or edit .env file with your API keys")
    
    print("\n2. 🚀 Run JARVIS:")
    if os.name == 'nt':  # Windows
        print("   .\\venv\\Scripts\\python.exe -m jarvis")
    else:  # Unix/Linux/macOS
        print("   ./venv/bin/python -m jarvis")
    
    print("\n3. 🎯 Available Interfaces:")
    print("   - CLI (default): --interface cli")
    print("   - Voice: --interface voice (coming soon)")
    print("   - GUI: --interface gui (coming soon)")
    
    print("\n4. 📚 Learn More:")
    print("   - Check README.md for detailed documentation")
    print("   - Use /help command in JARVIS for available commands")
    print("   - Visit the project repository for updates")
    
    print("\n💡 Note: JARVIS will run in test mode without API keys configured.")
    print("   Configure your API keys for full functionality!")
    print("\n" + "="*60)


def main():
    """Main setup function"""
    print("🤖 JARVIS AI Assistant Setup")
    print("="*40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create configuration files
    if not create_config_file():
        sys.exit(1)
    
    if not create_env_file():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("⚠️  Installation test failed, but setup may still be functional")
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    main()
