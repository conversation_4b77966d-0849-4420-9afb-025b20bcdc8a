"""
Proactive Assistance for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json
from pathlib import Path
import asyncio

from ..config import config

logger = logging.getLogger(__name__)


@dataclass
class ProactiveSuggestion:
    """Proactive suggestion representation"""
    suggestion_id: str
    title: str
    description: str
    action: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: str = "medium"  # low, medium, high, urgent
    confidence: float = 0.8
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UserPattern:
    """User behavior pattern"""
    pattern_id: str
    pattern_type: str  # temporal, sequential, contextual
    description: str
    frequency: int
    confidence: float
    last_observed: datetime
    conditions: Dict[str, Any] = field(default_factory=dict)
    actions: List[str] = field(default_factory=list)


class ProactiveAssistant:
    """Advanced proactive assistance system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize proactive assistant"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.suggestions_file = self.data_dir / "proactive_suggestions.json"
        self.patterns_file = self.data_dir / "user_patterns.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Storage
        self.active_suggestions: Dict[str, ProactiveSuggestion] = {}
        self.user_patterns: Dict[str, UserPattern] = {}
        self.suggestion_history: List[ProactiveSuggestion] = []
        
        # Configuration
        self.max_active_suggestions = config.get("proactive.max_active_suggestions", 5)
        self.suggestion_expiry_hours = config.get("proactive.suggestion_expiry_hours", 24)
        self.pattern_confidence_threshold = config.get("proactive.pattern_confidence_threshold", 0.7)
        
        # Suggestion generators
        self.suggestion_generators: Dict[str, Callable] = {}
        
        # Load existing data
        self._load_suggestions()
        self._load_patterns()
        
        # Register default generators
        self._register_default_generators()
        
        logger.info("Proactive assistant initialized")
    
    async def analyze_context_and_suggest(
        self,
        user_id: str,
        current_context: Dict[str, Any],
        recent_actions: List[str]
    ) -> List[ProactiveSuggestion]:
        """Analyze context and generate proactive suggestions"""
        suggestions = []
        
        # Update user patterns
        self._update_user_patterns(user_id, current_context, recent_actions)
        
        # Generate suggestions from all registered generators
        for generator_name, generator_func in self.suggestion_generators.items():
            try:
                generator_suggestions = await generator_func(user_id, current_context, recent_actions)
                suggestions.extend(generator_suggestions)
            except Exception as e:
                logger.error(f"Error in suggestion generator {generator_name}: {e}")
        
        # Filter and rank suggestions
        filtered_suggestions = self._filter_and_rank_suggestions(suggestions, current_context)
        
        # Store active suggestions
        for suggestion in filtered_suggestions:
            self.active_suggestions[suggestion.suggestion_id] = suggestion
        
        # Clean up expired suggestions
        self._cleanup_expired_suggestions()
        
        # Save suggestions
        self._save_suggestions()
        
        return filtered_suggestions
    
    def get_active_suggestions(self, user_id: str) -> List[ProactiveSuggestion]:
        """Get active suggestions for user"""
        user_suggestions = [
            suggestion for suggestion in self.active_suggestions.values()
            if suggestion.context.get("user_id") == user_id
        ]
        
        # Sort by priority and confidence
        priority_order = {"urgent": 0, "high": 1, "medium": 2, "low": 3}
        user_suggestions.sort(key=lambda s: (
            priority_order.get(s.priority, 4),
            -s.confidence,
            s.created_at
        ))
        
        return user_suggestions
    
    def accept_suggestion(self, suggestion_id: str) -> bool:
        """Mark suggestion as accepted"""
        if suggestion_id in self.active_suggestions:
            suggestion = self.active_suggestions[suggestion_id]
            suggestion.metadata["accepted"] = True
            suggestion.metadata["accepted_at"] = datetime.now().isoformat()
            
            # Move to history
            self.suggestion_history.append(suggestion)
            del self.active_suggestions[suggestion_id]
            
            # Update pattern confidence
            self._update_pattern_confidence(suggestion, accepted=True)
            
            self._save_suggestions()
            logger.info(f"Suggestion accepted: {suggestion.title}")
            return True
        
        return False
    
    def dismiss_suggestion(self, suggestion_id: str) -> bool:
        """Dismiss suggestion"""
        if suggestion_id in self.active_suggestions:
            suggestion = self.active_suggestions[suggestion_id]
            suggestion.metadata["dismissed"] = True
            suggestion.metadata["dismissed_at"] = datetime.now().isoformat()
            
            # Move to history
            self.suggestion_history.append(suggestion)
            del self.active_suggestions[suggestion_id]
            
            # Update pattern confidence
            self._update_pattern_confidence(suggestion, accepted=False)
            
            self._save_suggestions()
            logger.info(f"Suggestion dismissed: {suggestion.title}")
            return True
        
        return False
    
    def register_suggestion_generator(self, name: str, generator_func: Callable):
        """Register custom suggestion generator"""
        self.suggestion_generators[name] = generator_func
        logger.info(f"Registered suggestion generator: {name}")
    
    def _register_default_generators(self):
        """Register default suggestion generators"""
        
        async def temporal_pattern_generator(
            user_id: str,
            context: Dict[str, Any],
            recent_actions: List[str]
        ) -> List[ProactiveSuggestion]:
            """Generate suggestions based on temporal patterns"""
            suggestions = []
            current_time = datetime.now()
            current_hour = current_time.hour
            current_day = current_time.strftime("%A")
            
            # Find temporal patterns for this time
            for pattern in self.user_patterns.values():
                if (pattern.pattern_type == "temporal" and 
                    pattern.confidence >= self.pattern_confidence_threshold):
                    
                    pattern_hour = pattern.conditions.get("hour")
                    pattern_day = pattern.conditions.get("day")
                    
                    if ((pattern_hour is None or pattern_hour == current_hour) and
                        (pattern_day is None or pattern_day == current_day)):
                        
                        # Generate suggestion based on pattern
                        suggestion = ProactiveSuggestion(
                            suggestion_id=f"temporal_{pattern.pattern_id}_{current_time.timestamp()}",
                            title=f"Time for {pattern.description}",
                            description=f"You usually {pattern.description} at this time",
                            action="suggest_pattern_action",
                            parameters={"pattern_id": pattern.pattern_id, "actions": pattern.actions},
                            priority="medium",
                            confidence=pattern.confidence,
                            context={"user_id": user_id, "pattern_type": "temporal"}
                        )
                        suggestions.append(suggestion)
            
            return suggestions
        
        async def task_completion_generator(
            user_id: str,
            context: Dict[str, Any],
            recent_actions: List[str]
        ) -> List[ProactiveSuggestion]:
            """Generate suggestions for task completion"""
            suggestions = []
            
            # Check for incomplete tasks or workflows
            if "incomplete_tasks" in context:
                incomplete_tasks = context["incomplete_tasks"]
                
                if len(incomplete_tasks) > 5:
                    suggestion = ProactiveSuggestion(
                        suggestion_id=f"task_cleanup_{datetime.now().timestamp()}",
                        title="Clean up task list",
                        description=f"You have {len(incomplete_tasks)} incomplete tasks. Would you like to review and organize them?",
                        action="organize_tasks",
                        parameters={"task_count": len(incomplete_tasks)},
                        priority="medium",
                        confidence=0.8,
                        context={"user_id": user_id, "suggestion_type": "task_management"}
                    )
                    suggestions.append(suggestion)
            
            return suggestions
        
        async def system_maintenance_generator(
            user_id: str,
            context: Dict[str, Any],
            recent_actions: List[str]
        ) -> List[ProactiveSuggestion]:
            """Generate system maintenance suggestions"""
            suggestions = []
            
            # Check system resources
            if "system_info" in context:
                system_info = context["system_info"]
                
                # High memory usage
                if system_info.get("memory_percent", 0) > 80:
                    suggestion = ProactiveSuggestion(
                        suggestion_id=f"memory_cleanup_{datetime.now().timestamp()}",
                        title="High memory usage detected",
                        description="Your system is using over 80% of available memory. Would you like me to help optimize it?",
                        action="optimize_memory",
                        parameters={"memory_percent": system_info["memory_percent"]},
                        priority="high",
                        confidence=0.9,
                        context={"user_id": user_id, "suggestion_type": "system_maintenance"}
                    )
                    suggestions.append(suggestion)
                
                # Low disk space
                if system_info.get("disk_percent", 0) > 90:
                    suggestion = ProactiveSuggestion(
                        suggestion_id=f"disk_cleanup_{datetime.now().timestamp()}",
                        title="Low disk space",
                        description="Your disk is over 90% full. Would you like me to help clean up unnecessary files?",
                        action="cleanup_disk",
                        parameters={"disk_percent": system_info["disk_percent"]},
                        priority="high",
                        confidence=0.9,
                        context={"user_id": user_id, "suggestion_type": "system_maintenance"}
                    )
                    suggestions.append(suggestion)
            
            return suggestions
        
        async def learning_opportunity_generator(
            user_id: str,
            context: Dict[str, Any],
            recent_actions: List[str]
        ) -> List[ProactiveSuggestion]:
            """Generate learning opportunity suggestions"""
            suggestions = []
            
            # Analyze recent actions for learning opportunities
            action_types = {}
            for action in recent_actions[-20:]:  # Last 20 actions
                action_type = action.split("_")[0] if "_" in action else action
                action_types[action_type] = action_types.get(action_type, 0) + 1
            
            # Suggest advanced features for frequently used actions
            for action_type, count in action_types.items():
                if count >= 5:  # Used 5+ times recently
                    suggestion = ProactiveSuggestion(
                        suggestion_id=f"learning_{action_type}_{datetime.now().timestamp()}",
                        title=f"Learn advanced {action_type} features",
                        description=f"You've been using {action_type} frequently. Would you like to learn about advanced features?",
                        action="show_advanced_features",
                        parameters={"feature_type": action_type},
                        priority="low",
                        confidence=0.6,
                        context={"user_id": user_id, "suggestion_type": "learning"}
                    )
                    suggestions.append(suggestion)
            
            return suggestions
        
        # Register generators
        self.register_suggestion_generator("temporal_patterns", temporal_pattern_generator)
        self.register_suggestion_generator("task_completion", task_completion_generator)
        self.register_suggestion_generator("system_maintenance", system_maintenance_generator)
        self.register_suggestion_generator("learning_opportunities", learning_opportunity_generator)
    
    def _update_user_patterns(
        self,
        user_id: str,
        context: Dict[str, Any],
        recent_actions: List[str]
    ):
        """Update user behavior patterns"""
        current_time = datetime.now()
        
        # Temporal pattern analysis
        if recent_actions:
            current_hour = current_time.hour
            current_day = current_time.strftime("%A")
            
            # Create or update temporal pattern
            pattern_key = f"temporal_{user_id}_{current_hour}_{current_day}"
            
            if pattern_key in self.user_patterns:
                pattern = self.user_patterns[pattern_key]
                pattern.frequency += 1
                pattern.last_observed = current_time
                pattern.actions.extend(recent_actions[-3:])  # Add recent actions
                
                # Update confidence based on frequency
                pattern.confidence = min(0.95, pattern.frequency / 10.0)
            else:
                pattern = UserPattern(
                    pattern_id=pattern_key,
                    pattern_type="temporal",
                    description=f"Activity on {current_day} at {current_hour}:00",
                    frequency=1,
                    confidence=0.1,
                    last_observed=current_time,
                    conditions={"hour": current_hour, "day": current_day},
                    actions=recent_actions[-3:]
                )
                self.user_patterns[pattern_key] = pattern
        
        # Sequential pattern analysis
        if len(recent_actions) >= 3:
            sequence = tuple(recent_actions[-3:])
            pattern_key = f"sequential_{user_id}_{hash(sequence)}"
            
            if pattern_key in self.user_patterns:
                pattern = self.user_patterns[pattern_key]
                pattern.frequency += 1
                pattern.last_observed = current_time
                pattern.confidence = min(0.95, pattern.frequency / 5.0)
            else:
                pattern = UserPattern(
                    pattern_id=pattern_key,
                    pattern_type="sequential",
                    description=f"Action sequence: {' -> '.join(sequence)}",
                    frequency=1,
                    confidence=0.2,
                    last_observed=current_time,
                    conditions={"sequence": list(sequence)},
                    actions=list(sequence)
                )
                self.user_patterns[pattern_key] = pattern
        
        # Save patterns
        self._save_patterns()
    
    def _filter_and_rank_suggestions(
        self,
        suggestions: List[ProactiveSuggestion],
        context: Dict[str, Any]
    ) -> List[ProactiveSuggestion]:
        """Filter and rank suggestions"""
        # Remove duplicates
        unique_suggestions = {}
        for suggestion in suggestions:
            key = f"{suggestion.action}_{suggestion.title}"
            if key not in unique_suggestions or suggestion.confidence > unique_suggestions[key].confidence:
                unique_suggestions[key] = suggestion
        
        filtered_suggestions = list(unique_suggestions.values())
        
        # Filter by confidence threshold
        filtered_suggestions = [
            s for s in filtered_suggestions 
            if s.confidence >= 0.5
        ]
        
        # Sort by priority and confidence
        priority_order = {"urgent": 0, "high": 1, "medium": 2, "low": 3}
        filtered_suggestions.sort(key=lambda s: (
            priority_order.get(s.priority, 4),
            -s.confidence
        ))
        
        # Limit to max active suggestions
        return filtered_suggestions[:self.max_active_suggestions]
    
    def _update_pattern_confidence(self, suggestion: ProactiveSuggestion, accepted: bool):
        """Update pattern confidence based on suggestion feedback"""
        pattern_id = suggestion.parameters.get("pattern_id")
        if pattern_id and pattern_id in self.user_patterns:
            pattern = self.user_patterns[pattern_id]
            
            if accepted:
                pattern.confidence = min(0.95, pattern.confidence + 0.1)
            else:
                pattern.confidence = max(0.1, pattern.confidence - 0.05)
            
            self._save_patterns()
    
    def _cleanup_expired_suggestions(self):
        """Clean up expired suggestions"""
        current_time = datetime.now()
        expired_suggestions = []
        
        for suggestion_id, suggestion in self.active_suggestions.items():
            if (suggestion.expires_at and suggestion.expires_at < current_time) or \
               (current_time - suggestion.created_at).total_seconds() > self.suggestion_expiry_hours * 3600:
                expired_suggestions.append(suggestion_id)
        
        for suggestion_id in expired_suggestions:
            suggestion = self.active_suggestions[suggestion_id]
            suggestion.metadata["expired"] = True
            self.suggestion_history.append(suggestion)
            del self.active_suggestions[suggestion_id]
        
        if expired_suggestions:
            logger.info(f"Cleaned up {len(expired_suggestions)} expired suggestions")
    
    def get_suggestion_statistics(self) -> Dict[str, Any]:
        """Get suggestion statistics"""
        total_suggestions = len(self.suggestion_history) + len(self.active_suggestions)
        accepted_suggestions = sum(1 for s in self.suggestion_history if s.metadata.get("accepted"))
        dismissed_suggestions = sum(1 for s in self.suggestion_history if s.metadata.get("dismissed"))
        
        acceptance_rate = (accepted_suggestions / total_suggestions * 100) if total_suggestions > 0 else 0
        
        # Suggestion types
        suggestion_types = {}
        for suggestion in list(self.active_suggestions.values()) + self.suggestion_history:
            suggestion_type = suggestion.context.get("suggestion_type", "unknown")
            suggestion_types[suggestion_type] = suggestion_types.get(suggestion_type, 0) + 1
        
        return {
            "total_suggestions": total_suggestions,
            "active_suggestions": len(self.active_suggestions),
            "accepted_suggestions": accepted_suggestions,
            "dismissed_suggestions": dismissed_suggestions,
            "acceptance_rate": round(acceptance_rate, 2),
            "suggestion_types": suggestion_types,
            "total_patterns": len(self.user_patterns),
            "high_confidence_patterns": sum(1 for p in self.user_patterns.values() if p.confidence >= 0.8)
        }
    
    def _load_suggestions(self):
        """Load suggestions from file"""
        try:
            if self.suggestions_file.exists():
                with open(self.suggestions_file, 'r') as f:
                    suggestions_data = json.load(f)
                
                # Load active suggestions
                for suggestion_id, suggestion_dict in suggestions_data.get("active", {}).items():
                    suggestion_dict["created_at"] = datetime.fromisoformat(suggestion_dict["created_at"])
                    if suggestion_dict.get("expires_at"):
                        suggestion_dict["expires_at"] = datetime.fromisoformat(suggestion_dict["expires_at"])
                    
                    suggestion = ProactiveSuggestion(**suggestion_dict)
                    self.active_suggestions[suggestion_id] = suggestion
                
                # Load suggestion history
                for suggestion_dict in suggestions_data.get("history", []):
                    suggestion_dict["created_at"] = datetime.fromisoformat(suggestion_dict["created_at"])
                    if suggestion_dict.get("expires_at"):
                        suggestion_dict["expires_at"] = datetime.fromisoformat(suggestion_dict["expires_at"])
                    
                    suggestion = ProactiveSuggestion(**suggestion_dict)
                    self.suggestion_history.append(suggestion)
                
                logger.info(f"Loaded {len(self.active_suggestions)} active suggestions and {len(self.suggestion_history)} historical suggestions")
                
        except Exception as e:
            logger.error(f"Error loading suggestions: {e}")
    
    def _save_suggestions(self):
        """Save suggestions to file"""
        try:
            suggestions_data = {
                "active": {},
                "history": []
            }
            
            # Save active suggestions
            for suggestion_id, suggestion in self.active_suggestions.items():
                suggestion_dict = {
                    "suggestion_id": suggestion.suggestion_id,
                    "title": suggestion.title,
                    "description": suggestion.description,
                    "action": suggestion.action,
                    "parameters": suggestion.parameters,
                    "priority": suggestion.priority,
                    "confidence": suggestion.confidence,
                    "created_at": suggestion.created_at.isoformat(),
                    "expires_at": suggestion.expires_at.isoformat() if suggestion.expires_at else None,
                    "context": suggestion.context,
                    "metadata": suggestion.metadata
                }
                suggestions_data["active"][suggestion_id] = suggestion_dict
            
            # Save suggestion history (last 100)
            for suggestion in self.suggestion_history[-100:]:
                suggestion_dict = {
                    "suggestion_id": suggestion.suggestion_id,
                    "title": suggestion.title,
                    "description": suggestion.description,
                    "action": suggestion.action,
                    "parameters": suggestion.parameters,
                    "priority": suggestion.priority,
                    "confidence": suggestion.confidence,
                    "created_at": suggestion.created_at.isoformat(),
                    "expires_at": suggestion.expires_at.isoformat() if suggestion.expires_at else None,
                    "context": suggestion.context,
                    "metadata": suggestion.metadata
                }
                suggestions_data["history"].append(suggestion_dict)
            
            with open(self.suggestions_file, 'w') as f:
                json.dump(suggestions_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving suggestions: {e}")
    
    def _load_patterns(self):
        """Load user patterns from file"""
        try:
            if self.patterns_file.exists():
                with open(self.patterns_file, 'r') as f:
                    patterns_data = json.load(f)
                
                for pattern_id, pattern_dict in patterns_data.items():
                    pattern_dict["last_observed"] = datetime.fromisoformat(pattern_dict["last_observed"])
                    pattern = UserPattern(**pattern_dict)
                    self.user_patterns[pattern_id] = pattern
                
                logger.info(f"Loaded {len(self.user_patterns)} user patterns")
                
        except Exception as e:
            logger.error(f"Error loading patterns: {e}")
    
    def _save_patterns(self):
        """Save user patterns to file"""
        try:
            patterns_data = {}
            
            for pattern_id, pattern in self.user_patterns.items():
                pattern_dict = {
                    "pattern_id": pattern.pattern_id,
                    "pattern_type": pattern.pattern_type,
                    "description": pattern.description,
                    "frequency": pattern.frequency,
                    "confidence": pattern.confidence,
                    "last_observed": pattern.last_observed.isoformat(),
                    "conditions": pattern.conditions,
                    "actions": pattern.actions
                }
                patterns_data[pattern_id] = pattern_dict
            
            with open(self.patterns_file, 'w') as f:
                json.dump(patterns_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving patterns: {e}")
