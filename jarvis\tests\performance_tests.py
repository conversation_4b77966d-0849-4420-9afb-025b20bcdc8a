"""
Performance Tests for JARVIS AI Assistant
"""

import time
import asyncio
import logging
import psutil
from typing import Dict, List, Any, Optional
from datetime import datetime
import concurrent.futures

logger = logging.getLogger(__name__)


class PerformanceTestSuite:
    """Performance testing suite for JARVIS"""
    
    def __init__(self):
        """Initialize performance test suite"""
        self.test_type = "performance"
        self.results = {}
        
    async def test_nlp_processing_speed(self) -> Dict[str, Any]:
        """Test NLP processing speed"""
        try:
            from jarvis.core.nlp.processor import NLPProcessor
            
            processor = NLPProcessor()
            test_messages = [
                "What's the weather like today?",
                "Create a new file called test.txt",
                "Show me system information",
                "Search for Python tutorials online",
                "Set a reminder for 3 PM tomorrow"
            ]
            
            start_time = time.time()
            
            for message in test_messages:
                await processor.process_message(message)
                
            end_time = time.time()
            total_time = end_time - start_time
            avg_time = total_time / len(test_messages)
            
            return {
                "test_name": "nlp_processing_speed",
                "total_messages": len(test_messages),
                "total_time": total_time,
                "average_time_per_message": avg_time,
                "messages_per_second": len(test_messages) / total_time,
                "status": "passed" if avg_time < 1.0 else "warning"
            }
            
        except Exception as e:
            return {
                "test_name": "nlp_processing_speed",
                "status": "error",
                "error": str(e)
            }
            
    async def test_task_execution_speed(self) -> Dict[str, Any]:
        """Test task execution speed"""
        try:
            from jarvis.core.tasks.executor import TaskExecutor
            from jarvis.core.tasks.task import Task
            
            executor = TaskExecutor()
            
            # Create test tasks
            test_tasks = [
                Task("test_task_1", "help", {}),
                Task("test_task_2", "status", {}),
                Task("test_task_3", "time", {}),
                Task("test_task_4", "echo", {"message": "test"}),
                Task("test_task_5", "ping", {})
            ]
            
            start_time = time.time()
            
            for task in test_tasks:
                await executor.execute_task(task)
                
            end_time = time.time()
            total_time = end_time - start_time
            avg_time = total_time / len(test_tasks)
            
            return {
                "test_name": "task_execution_speed",
                "total_tasks": len(test_tasks),
                "total_time": total_time,
                "average_time_per_task": avg_time,
                "tasks_per_second": len(test_tasks) / total_time,
                "status": "passed" if avg_time < 0.5 else "warning"
            }
            
        except Exception as e:
            return {
                "test_name": "task_execution_speed",
                "status": "error",
                "error": str(e)
            }
            
    async def test_concurrent_processing(self) -> Dict[str, Any]:
        """Test concurrent processing capabilities"""
        try:
            from jarvis.core.nlp.processor import NLPProcessor
            
            processor = NLPProcessor()
            test_messages = [
                "What's the weather?",
                "Show system info",
                "Create a file",
                "Search online",
                "Set reminder"
            ] * 10  # 50 messages total
            
            start_time = time.time()
            
            # Process messages concurrently
            tasks = [processor.process_message(msg) for msg in test_messages]
            await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            return {
                "test_name": "concurrent_processing",
                "total_messages": len(test_messages),
                "total_time": total_time,
                "messages_per_second": len(test_messages) / total_time,
                "status": "passed" if total_time < 10.0 else "warning"
            }
            
        except Exception as e:
            return {
                "test_name": "concurrent_processing",
                "status": "error",
                "error": str(e)
            }
            
    def test_memory_usage(self) -> Dict[str, Any]:
        """Test memory usage"""
        try:
            process = psutil.Process()
            
            # Get initial memory usage
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Simulate some operations
            data = []
            for i in range(1000):
                data.append(f"test_data_{i}" * 100)
                
            # Get peak memory usage
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Clean up
            del data
            
            # Get final memory usage
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            memory_increase = peak_memory - initial_memory
            memory_cleanup = peak_memory - final_memory
            
            return {
                "test_name": "memory_usage",
                "initial_memory_mb": initial_memory,
                "peak_memory_mb": peak_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": memory_increase,
                "memory_cleanup_mb": memory_cleanup,
                "status": "passed" if memory_increase < 100 else "warning"
            }
            
        except Exception as e:
            return {
                "test_name": "memory_usage",
                "status": "error",
                "error": str(e)
            }
            
    def test_cpu_usage(self) -> Dict[str, Any]:
        """Test CPU usage during operations"""
        try:
            # Monitor CPU usage
            cpu_samples = []
            
            def monitor_cpu():
                for _ in range(10):
                    cpu_samples.append(psutil.cpu_percent(interval=0.1))
                    
            # Start CPU monitoring
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cpu_future = executor.submit(monitor_cpu)
                
                # Simulate CPU-intensive operations
                start_time = time.time()
                
                # Some computational work
                result = 0
                for i in range(1000000):
                    result += i * i
                    
                end_time = time.time()
                
                # Wait for CPU monitoring to complete
                cpu_future.result()
                
            avg_cpu = sum(cpu_samples) / len(cpu_samples)
            max_cpu = max(cpu_samples)
            
            return {
                "test_name": "cpu_usage",
                "operation_time": end_time - start_time,
                "average_cpu_percent": avg_cpu,
                "max_cpu_percent": max_cpu,
                "cpu_samples": len(cpu_samples),
                "status": "passed" if avg_cpu < 80 else "warning"
            }
            
        except Exception as e:
            return {
                "test_name": "cpu_usage",
                "status": "error",
                "error": str(e)
            }
            
    async def test_response_time_consistency(self) -> Dict[str, Any]:
        """Test response time consistency"""
        try:
            from jarvis.core.nlp.processor import NLPProcessor
            
            processor = NLPProcessor()
            test_message = "What time is it?"
            
            response_times = []
            
            # Run the same operation multiple times
            for _ in range(20):
                start_time = time.time()
                await processor.process_message(test_message)
                end_time = time.time()
                response_times.append(end_time - start_time)
                
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            # Calculate standard deviation
            variance = sum((t - avg_time) ** 2 for t in response_times) / len(response_times)
            std_dev = variance ** 0.5
            
            # Consistency score (lower is better)
            consistency_score = std_dev / avg_time
            
            return {
                "test_name": "response_time_consistency",
                "iterations": len(response_times),
                "average_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "standard_deviation": std_dev,
                "consistency_score": consistency_score,
                "status": "passed" if consistency_score < 0.3 else "warning"
            }
            
        except Exception as e:
            return {
                "test_name": "response_time_consistency",
                "status": "error",
                "error": str(e)
            }
            
    async def run_all_performance_tests(self) -> Dict[str, Any]:
        """Run all performance tests"""
        logger.info("Starting performance test suite")
        
        test_results = {}
        
        # Run async tests
        async_tests = [
            self.test_nlp_processing_speed(),
            self.test_task_execution_speed(),
            self.test_concurrent_processing(),
            self.test_response_time_consistency()
        ]
        
        for test_coro in async_tests:
            try:
                result = await test_coro
                test_results[result["test_name"]] = result
            except Exception as e:
                logger.error(f"Error in async performance test: {e}")
                
        # Run sync tests
        sync_tests = [
            self.test_memory_usage,
            self.test_cpu_usage
        ]
        
        for test_func in sync_tests:
            try:
                result = test_func()
                test_results[result["test_name"]] = result
            except Exception as e:
                logger.error(f"Error in sync performance test: {e}")
                
        # Calculate overall performance score
        passed_tests = sum(1 for result in test_results.values() if result.get("status") == "passed")
        total_tests = len(test_results)
        performance_score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "performance_score": performance_score,
            "test_results": test_results
        }
        
        logger.info(f"Performance test suite completed: {passed_tests}/{total_tests} tests passed")
        
        return summary
