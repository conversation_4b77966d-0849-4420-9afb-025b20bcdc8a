"""
System Validators for JARVIS AI Assistant
"""

import logging
import sys
import importlib
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class SystemValidator:
    """System validation and health checks"""
    
    def __init__(self):
        """Initialize system validator"""
        self.validation_results = {}
        
    def validate_all(self) -> Dict[str, Any]:
        """Run all system validations"""
        results = {
            "python_version": self.validate_python_version(),
            "dependencies": self.validate_dependencies(),
            "modules": self.validate_modules(),
            "configuration": self.validate_configuration(),
            "file_structure": self.validate_file_structure()
        }
        
        self.validation_results = results
        return results
        
    def validate_python_version(self) -> Dict[str, Any]:
        """Validate Python version"""
        try:
            version = sys.version_info
            is_valid = version.major >= 3 and version.minor >= 8
            
            return {
                "valid": is_valid,
                "version": f"{version.major}.{version.minor}.{version.micro}",
                "message": "Python version is compatible" if is_valid else "Python 3.8+ required"
            }
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "message": "Failed to check Python version"
            }
            
    def validate_dependencies(self) -> Dict[str, Any]:
        """Validate required dependencies"""
        required_packages = [
            "openai",
            "asyncio",
            "pathlib",
            "json",
            "logging",
            "datetime",
            "typing"
        ]
        
        optional_packages = [
            "cryptography",
            "jwt",
            "bcrypt",
            "aiohttp",
            "socketio"
        ]
        
        results = {
            "required": {},
            "optional": {},
            "all_required_available": True
        }
        
        # Check required packages
        for package in required_packages:
            try:
                importlib.import_module(package)
                results["required"][package] = {"available": True, "error": None}
            except ImportError as e:
                results["required"][package] = {"available": False, "error": str(e)}
                results["all_required_available"] = False
                
        # Check optional packages
        for package in optional_packages:
            try:
                importlib.import_module(package)
                results["optional"][package] = {"available": True, "error": None}
            except ImportError as e:
                results["optional"][package] = {"available": False, "error": str(e)}
                
        return results
        
    def validate_modules(self) -> Dict[str, Any]:
        """Validate JARVIS modules"""
        modules_to_check = [
            "jarvis.config",
            "jarvis.utils.logging",
            "jarvis.core.nlp.processor",
            "jarvis.core.tasks.executor",
            "jarvis.interfaces.cli"
        ]
        
        results = {
            "modules": {},
            "all_modules_available": True
        }
        
        for module_name in modules_to_check:
            try:
                importlib.import_module(module_name)
                results["modules"][module_name] = {"available": True, "error": None}
            except ImportError as e:
                results["modules"][module_name] = {"available": False, "error": str(e)}
                results["all_modules_available"] = False
                
        return results
        
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration"""
        try:
            from jarvis.config import config
            
            # Check if config loads
            config_data = config.get_all()
            
            # Check required config sections
            required_sections = ["openai", "logging", "data_dir"]
            missing_sections = []
            
            for section in required_sections:
                if section not in config_data:
                    missing_sections.append(section)
                    
            return {
                "valid": len(missing_sections) == 0,
                "missing_sections": missing_sections,
                "config_loaded": True,
                "message": "Configuration is valid" if len(missing_sections) == 0 else f"Missing sections: {missing_sections}"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "config_loaded": False,
                "error": str(e),
                "message": "Failed to load configuration"
            }
            
    def validate_file_structure(self) -> Dict[str, Any]:
        """Validate file structure"""
        project_root = Path(__file__).parent.parent
        
        required_files = [
            "jarvis/__init__.py",
            "jarvis/__main__.py",
            "jarvis/config/__init__.py",
            "jarvis/config/config.yaml",
            "jarvis/utils/__init__.py",
            "jarvis/utils/logging.py",
            "jarvis/core/__init__.py",
            "jarvis/interfaces/__init__.py"
        ]
        
        required_directories = [
            "jarvis",
            "jarvis/config",
            "jarvis/utils",
            "jarvis/core",
            "jarvis/interfaces",
            "jarvis/core/nlp",
            "jarvis/core/tasks"
        ]
        
        results = {
            "files": {},
            "directories": {},
            "all_files_present": True,
            "all_directories_present": True
        }
        
        # Check files
        for file_path in required_files:
            full_path = project_root / file_path
            exists = full_path.exists()
            results["files"][file_path] = {"exists": exists}
            if not exists:
                results["all_files_present"] = False
                
        # Check directories
        for dir_path in required_directories:
            full_path = project_root / dir_path
            exists = full_path.exists() and full_path.is_dir()
            results["directories"][dir_path] = {"exists": exists}
            if not exists:
                results["all_directories_present"] = False
                
        return results
        
    def get_health_status(self) -> str:
        """Get overall system health status"""
        if not self.validation_results:
            self.validate_all()
            
        issues = []
        
        # Check Python version
        if not self.validation_results["python_version"]["valid"]:
            issues.append("Python version incompatible")
            
        # Check dependencies
        if not self.validation_results["dependencies"]["all_required_available"]:
            issues.append("Missing required dependencies")
            
        # Check modules
        if not self.validation_results["modules"]["all_modules_available"]:
            issues.append("Missing JARVIS modules")
            
        # Check configuration
        if not self.validation_results["configuration"]["valid"]:
            issues.append("Configuration issues")
            
        # Check file structure
        if not self.validation_results["file_structure"]["all_files_present"]:
            issues.append("Missing required files")
            
        if not issues:
            return "healthy"
        elif len(issues) <= 2:
            return "warning"
        else:
            return "critical"
            
    def generate_report(self) -> str:
        """Generate validation report"""
        if not self.validation_results:
            self.validate_all()
            
        health_status = self.get_health_status()
        
        report = f"""
JARVIS System Validation Report
==============================

Overall Health: {health_status.upper()}

Python Version: {self.validation_results['python_version']['version']} 
Status: {'✓' if self.validation_results['python_version']['valid'] else '✗'}

Dependencies:
- Required: {'✓' if self.validation_results['dependencies']['all_required_available'] else '✗'}
- Optional packages available: {sum(1 for pkg in self.validation_results['dependencies']['optional'].values() if pkg['available'])}

Modules:
- Core modules: {'✓' if self.validation_results['modules']['all_modules_available'] else '✗'}

Configuration:
- Status: {'✓' if self.validation_results['configuration']['valid'] else '✗'}

File Structure:
- Required files: {'✓' if self.validation_results['file_structure']['all_files_present'] else '✗'}
- Required directories: {'✓' if self.validation_results['file_structure']['all_directories_present'] else '✗'}
"""
        
        return report
