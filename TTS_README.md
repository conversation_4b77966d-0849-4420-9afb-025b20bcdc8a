# JARVIS Text-to-Speech (TTS) Module

The TTS module provides multiple text-to-speech providers for the JARVIS AI assistant.

## Supported Providers

### 1. pyttsx3 (Local TTS)
- **Pros**: Works offline, no API keys required
- **Cons**: Limited voice quality, platform-dependent voices
- **Installation**: `pip install pyttsx3`

### 2. Google Text-to-Speech (gTTS)
- **Pros**: Good quality, free, many languages
- **Cons**: Requires internet connection
- **Installation**: `pip install gtts pygame`

### 3. ElevenLabs
- **Pros**: Excellent quality, realistic voices
- **Cons**: Requires API key and subscription
- **Setup**: Add ElevenLabs API key to config

### 4. Azure Speech Services
- **Pros**: High quality, many voices and languages
- **Cons**: Requires Azure subscription and API key
- **Installation**: `pip install azure-cognitiveservices-speech`

## Installation

Install all dependencies:
```bash
pip install -r requirements-tts.txt
```

Or install selectively based on your needs:
```bash
# For offline TTS
pip install pyttsx3

# For Google TTS
pip install gtts pygame

# For Azure TTS
pip install azure-cognitiveservices-speech
```

## Configuration

Add to your JARVIS config file:

```yaml
voice:
  tts:
    provider: "pyttsx3"  # or "gtts", "elevenlabs", "azure"
    voice_id: "default"
    language: "en-US"
    speed: 1.0
    pitch: 0.0
    azure_region: "eastus"  # for Azure

api_keys:
  elevenlabs: "your_elevenlabs_api_key"
  azure_speech: "your_azure_speech_key"
```

## Usage

```python
from jarvis.integrations.voice.text_to_speech import TextToSpeech

# Initialize TTS
tts = TextToSpeech()

# Speak text
result = await tts.speak("Hello, this is JARVIS!")

# Save to file
result = await tts.speak("Hello!", save_file="output.mp3")

# Get available voices
voices = await tts.get_voices()

# Change voice
tts.set_voice("voice_id")

# Get available providers
providers = tts.get_available_providers()
```

## Testing

Run the test script to verify functionality:
```bash
python test_tts.py
```

## Troubleshooting

1. **No providers available**: Install the required dependencies
2. **Audio playback issues**: Install pygame for audio playback
3. **API errors**: Check your API keys and internet connection
4. **Voice not found**: Use `get_voices()` to see available voices

## Error Handling

The module includes comprehensive error handling:
- Missing dependencies are handled gracefully
- API failures fall back to alternative providers
- All methods return success/error status
