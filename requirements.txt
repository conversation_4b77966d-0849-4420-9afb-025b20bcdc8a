# Core AI and Language Processing
openai>=1.0.0
anthropic>=0.7.0
langchain>=0.1.0
langchain-openai>=0.0.5
transformers>=4.30.0
torch>=2.0.0

# Speech Recognition and Text-to-Speech
openai-whisper>=20231117
SpeechRecognition>=3.10.0
PyAudio>=0.2.11
pyttsx3>=2.90
elevenlabs>=0.2.0
azure-cognitiveservices-speech>=1.30.0

# Web and Internet Capabilities
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0
playwright>=1.40.0
scrapy>=2.11.0
httpx>=0.25.0

# System Integration
psutil>=5.9.0
pyautogui>=0.9.54
keyboard>=0.13.5
mouse>=0.7.1
pygetwindow>=0.0.9
pyperclip>=1.8.2

# File and Data Management
watchdog>=3.0.0
python-magic>=0.4.27
pillow>=10.0.0
pandas>=2.0.0
numpy>=1.24.0

# Database and Memory
chromadb>=0.4.0
redis>=5.0.0
pymongo>=4.5.0

# Configuration and Environment
python-dotenv>=1.0.0
pyyaml>=6.0
click>=8.1.0
rich>=13.0.0
typer>=0.9.0

# GUI Framework
customtkinter>=5.2.0
PyQt6>=6.5.0
kivy>=2.2.0

# Async and Concurrency
aiohttp>=3.8.0
aiohttp-cors>=0.7.0
python-socketio>=5.8.0
uvloop>=0.17.0

# Security and Encryption
cryptography>=41.0.0
keyring>=24.0.0
bcrypt>=4.0.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Logging and Monitoring
loguru>=0.7.0
structlog>=23.0.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3
schedule>=1.2.0
tqdm>=4.66.0
