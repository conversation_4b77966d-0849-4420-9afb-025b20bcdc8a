# Text-to-Speech Dependencies for JARVIS
# Install these based on which TTS providers you want to use

# For pyttsx3 (local TTS, works offline)
pyttsx3>=2.90

# For Google Text-to-Speech (requires internet)
gtts>=2.3.0

# For audio playback (required for gTTS and ElevenLabs)
pygame>=2.1.0

# For Azure Speech Services (requires Azure subscription)
azure-cognitiveservices-speech>=1.24.0

# For ElevenLabs API (requires ElevenLabs API key)
# No additional package needed - uses requests which is already included

# Install all TTS dependencies:
# pip install -r requirements-tts.txt

# Or install selectively:
# pip install pyttsx3  # For offline TTS
# pip install gtts pygame  # For Google TTS
# pip install azure-cognitiveservices-speech  # For Azure TTS
