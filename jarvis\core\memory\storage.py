"""
Storage classes for JARVIS memory management
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

from ...config import config

logger = logging.getLogger(__name__)


class ConversationStorage:
    """Handles conversation history storage"""
    
    def __init__(self, data_dir: Path):
        """Initialize conversation storage"""
        self.data_dir = data_dir
        self.file_path = data_dir / "conversations.json"
        self.conversations: List[Dict[str, Any]] = []
        self._load_conversations()
    
    def _load_conversations(self):
        """Load conversations from file"""
        try:
            if self.file_path.exists():
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    self.conversations = json.load(f)
                logger.info(f"Loaded {len(self.conversations)} conversations")
        except Exception as e:
            logger.error(f"Error loading conversations: {e}")
            self.conversations = []
    
    def _save_conversations(self):
        """Save conversations to file"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.conversations, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving conversations: {e}")
    
    def add_exchange(
        self, 
        user_message: str, 
        assistant_response: str, 
        context: Optional[Dict[str, Any]] = None
    ):
        """Add a conversation exchange"""
        exchange = {
            "timestamp": datetime.now().isoformat(),
            "user_message": user_message,
            "assistant_response": assistant_response,
            "context": context or {}
        }
        
        self.conversations.append(exchange)
        
        # Keep only the last N conversations to prevent unlimited growth
        max_conversations = config.get("memory.conversation.max_history", 1000)
        if len(self.conversations) > max_conversations:
            self.conversations = self.conversations[-max_conversations:]
        
        self._save_conversations()
    
    def get_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get conversation history"""
        if limit:
            return self.conversations[-limit:]
        return self.conversations.copy()
    
    def search(self, query: str) -> List[Dict[str, Any]]:
        """Search conversations for a query"""
        query_lower = query.lower()
        results = []
        
        for conv in self.conversations:
            if (query_lower in conv["user_message"].lower() or 
                query_lower in conv["assistant_response"].lower()):
                results.append(conv)
        
        return results
    
    def clear(self):
        """Clear all conversations"""
        self.conversations.clear()
        self._save_conversations()
        logger.info("Conversation history cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        if not self.conversations:
            return {"count": 0, "size_mb": 0}
        
        file_size = 0
        if self.file_path.exists():
            file_size = self.file_path.stat().st_size / (1024 * 1024)
        
        return {
            "count": len(self.conversations),
            "size_mb": round(file_size, 2),
            "oldest": self.conversations[0]["timestamp"] if self.conversations else None,
            "newest": self.conversations[-1]["timestamp"] if self.conversations else None
        }


class PersonalStorage:
    """Handles personal information storage"""
    
    def __init__(self, data_dir: Path):
        """Initialize personal storage"""
        self.data_dir = data_dir
        self.file_path = data_dir / "personal.json"
        self.data: Dict[str, Any] = {}
        self._load_data()
    
    def _load_data(self):
        """Load personal data from file"""
        try:
            if self.file_path.exists():
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.info("Loaded personal information")
        except Exception as e:
            logger.error(f"Error loading personal data: {e}")
            self.data = {}
    
    def _save_data(self):
        """Save personal data to file"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving personal data: {e}")
    
    def set(self, key: str, value: Any):
        """Set a personal information item"""
        self.data[key] = value
        self._save_data()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a personal information item"""
        return self.data.get(key, default)
    
    def update(self, data: Dict[str, Any]):
        """Update multiple personal information items"""
        self.data.update(data)
        self._save_data()
    
    def get_all(self) -> Dict[str, Any]:
        """Get all personal information"""
        return self.data.copy()
    
    def delete(self, key: str) -> bool:
        """Delete a personal information item"""
        if key in self.data:
            del self.data[key]
            self._save_data()
            return True
        return False
    
    def clear(self):
        """Clear all personal information"""
        self.data.clear()
        self._save_data()
        logger.info("Personal information cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get personal storage statistics"""
        file_size = 0
        if self.file_path.exists():
            file_size = self.file_path.stat().st_size / (1024 * 1024)
        
        return {
            "items": len(self.data),
            "size_mb": round(file_size, 2),
            "keys": list(self.data.keys())
        }
