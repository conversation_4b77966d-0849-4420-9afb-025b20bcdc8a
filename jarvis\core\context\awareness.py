"""
Contextual Awareness System for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, Counter
import re

logger = logging.getLogger(__name__)


@dataclass
class ContextualInsight:
    """Represents a contextual insight about the user"""
    insight_type: str  # pattern, preference, behavior, temporal
    description: str
    confidence: float  # 0.0 to 1.0
    evidence: List[str]
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UserBehaviorPattern:
    """Represents a user behavior pattern"""
    pattern_id: str
    pattern_type: str  # temporal, sequential, preference, usage
    description: str
    frequency: int
    confidence: float
    examples: List[str]
    conditions: Dict[str, Any] = field(default_factory=dict)


class ContextualAwareness:
    """Advanced contextual awareness and user behavior understanding"""
    
    def __init__(self, memory_manager=None, context_manager=None):
        """Initialize contextual awareness system"""
        self.memory_manager = memory_manager
        self.context_manager = context_manager
        
        # Behavior tracking
        self.user_patterns: Dict[str, List[UserBehaviorPattern]] = defaultdict(list)
        self.contextual_insights: Dict[str, List[ContextualInsight]] = defaultdict(list)
        
        # Temporal patterns
        self.time_based_activities: Dict[str, Dict[int, List[str]]] = defaultdict(lambda: defaultdict(list))
        self.day_based_activities: Dict[str, Dict[str, List[str]]] = defaultdict(lambda: defaultdict(list))
        
        # Usage patterns
        self.command_sequences: Dict[str, List[List[str]]] = defaultdict(list)
        self.topic_transitions: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        
        logger.info("Contextual awareness system initialized")
    
    def analyze_user_message(
        self, 
        session_id: str, 
        message: str, 
        intent: str,
        entities: Dict[str, Any],
        timestamp: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Analyze a user message for contextual insights"""
        if not timestamp:
            timestamp = datetime.now()
        
        analysis = {
            "temporal_context": self._analyze_temporal_context(session_id, intent, timestamp),
            "behavioral_patterns": self._detect_behavioral_patterns(session_id, intent, entities),
            "preference_indicators": self._extract_preference_indicators(message, intent, entities),
            "contextual_relevance": self._assess_contextual_relevance(session_id, message, intent),
            "proactive_suggestions": self._generate_proactive_suggestions(session_id, intent, entities)
        }
        
        # Update behavior tracking
        self._update_behavior_tracking(session_id, intent, entities, timestamp)
        
        # Generate insights
        insights = self._generate_contextual_insights(session_id, analysis)
        if insights:
            analysis["new_insights"] = insights
        
        return analysis
    
    def get_contextual_suggestions(self, session_id: str, current_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get contextual suggestions based on user patterns"""
        suggestions = []
        
        # Time-based suggestions
        time_suggestions = self._get_time_based_suggestions(session_id)
        suggestions.extend(time_suggestions)
        
        # Pattern-based suggestions
        pattern_suggestions = self._get_pattern_based_suggestions(session_id, current_context)
        suggestions.extend(pattern_suggestions)
        
        # Context-aware suggestions
        context_suggestions = self._get_context_aware_suggestions(session_id, current_context)
        suggestions.extend(context_suggestions)
        
        # Sort by relevance and confidence
        suggestions.sort(key=lambda x: x.get("confidence", 0), reverse=True)
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def predict_user_intent(self, session_id: str, partial_message: str) -> List[Dict[str, Any]]:
        """Predict user intent based on partial input and context"""
        predictions = []
        
        # Get user patterns
        user_patterns = self.user_patterns.get(session_id, [])
        
        # Analyze partial message
        words = partial_message.lower().split()
        
        for pattern in user_patterns:
            if pattern.pattern_type == "sequential":
                # Check if partial message matches pattern start
                pattern_words = pattern.description.lower().split()
                if self._matches_pattern_start(words, pattern_words):
                    predictions.append({
                        "intent": pattern.conditions.get("intent", "unknown"),
                        "confidence": pattern.confidence * 0.8,  # Reduce confidence for partial match
                        "completion": pattern.description,
                        "pattern_type": "sequential"
                    })
        
        # Add frequency-based predictions
        if self.context_manager:
            context = self.context_manager.get_or_create_context(session_id)
            recent_intents = Counter(context.recent_intents)
            
            for intent, frequency in recent_intents.most_common(3):
                predictions.append({
                    "intent": intent,
                    "confidence": min(frequency / 10.0, 0.9),
                    "completion": f"Continue with {intent}",
                    "pattern_type": "frequency"
                })
        
        return sorted(predictions, key=lambda x: x["confidence"], reverse=True)
    
    def get_user_insights(self, session_id: str) -> Dict[str, Any]:
        """Get comprehensive user insights"""
        insights = {
            "behavioral_patterns": self._summarize_behavioral_patterns(session_id),
            "temporal_preferences": self._analyze_temporal_preferences(session_id),
            "usage_statistics": self._get_usage_statistics(session_id),
            "contextual_insights": self.contextual_insights.get(session_id, []),
            "recommendations": self._generate_personalization_recommendations(session_id)
        }
        
        return insights
    
    def _analyze_temporal_context(self, session_id: str, intent: str, timestamp: datetime) -> Dict[str, Any]:
        """Analyze temporal context of user activity"""
        hour = timestamp.hour
        day_of_week = timestamp.strftime("%A")
        
        # Track time-based activities
        self.time_based_activities[session_id][hour].append(intent)
        self.day_based_activities[session_id][day_of_week].append(intent)
        
        # Analyze patterns
        hour_activities = self.time_based_activities[session_id][hour]
        day_activities = self.day_based_activities[session_id][day_of_week]
        
        return {
            "current_hour": hour,
            "current_day": day_of_week,
            "typical_hour_activities": Counter(hour_activities).most_common(3),
            "typical_day_activities": Counter(day_activities).most_common(3),
            "is_unusual_time": self._is_unusual_activity_time(session_id, intent, hour, day_of_week)
        }
    
    def _detect_behavioral_patterns(self, session_id: str, intent: str, entities: Dict[str, Any]) -> List[str]:
        """Detect behavioral patterns in user interactions"""
        patterns = []
        
        # Sequential pattern detection
        if session_id in self.command_sequences:
            recent_commands = self.command_sequences[session_id][-5:]  # Last 5 command sequences
            
            # Look for repeated sequences
            current_sequence = [cmd for seq in recent_commands for cmd in seq] + [intent]
            
            if len(current_sequence) >= 3:
                # Check for 3-command patterns
                pattern = tuple(current_sequence[-3:])
                pattern_count = sum(1 for seq in recent_commands if tuple(seq[-3:]) == pattern)
                
                if pattern_count >= 2:
                    patterns.append(f"Sequential pattern: {' -> '.join(pattern)}")
        
        # Entity-based patterns
        if entities:
            for entity_type, entity_values in entities.items():
                if len(entity_values) > 0:
                    # Track entity usage patterns
                    entity_key = f"{intent}_{entity_type}"
                    patterns.append(f"Entity pattern: {entity_key}")
        
        return patterns
    
    def _extract_preference_indicators(self, message: str, intent: str, entities: Dict[str, Any]) -> Dict[str, Any]:
        """Extract user preference indicators from message"""
        preferences = {}
        
        # Communication style preferences
        if len(message.split()) > 20:
            preferences["communication_style"] = "detailed"
        elif len(message.split()) < 5:
            preferences["communication_style"] = "concise"
        
        # Formality level
        formal_indicators = ["please", "could you", "would you", "thank you"]
        casual_indicators = ["hey", "yo", "sup", "thx", "k"]
        
        formal_count = sum(1 for indicator in formal_indicators if indicator in message.lower())
        casual_count = sum(1 for indicator in casual_indicators if indicator in message.lower())
        
        if formal_count > casual_count:
            preferences["formality"] = "formal"
        elif casual_count > formal_count:
            preferences["formality"] = "casual"
        
        # Task preferences
        if intent in ["file_operation", "system_control"]:
            preferences["task_complexity"] = "advanced"
        elif intent in ["get_weather", "simple_search"]:
            preferences["task_complexity"] = "basic"
        
        return preferences
    
    def _assess_contextual_relevance(self, session_id: str, message: str, intent: str) -> float:
        """Assess how relevant the current message is to the ongoing context"""
        if not self.context_manager:
            return 0.5
        
        context = self.context_manager.get_or_create_context(session_id)
        
        relevance_score = 0.0
        
        # Topic continuity
        if context.current_topic:
            topic_keywords = {
                "mathematics": ["calculate", "math", "number", "equation"],
                "file_management": ["file", "folder", "directory", "save", "open"],
                "weather": ["weather", "temperature", "rain", "sunny", "cloudy"],
                "system_management": ["system", "process", "memory", "cpu"]
            }
            
            if context.current_topic in topic_keywords:
                keywords = topic_keywords[context.current_topic]
                keyword_matches = sum(1 for keyword in keywords if keyword in message.lower())
                relevance_score += keyword_matches / len(keywords) * 0.4
        
        # Intent continuity
        if context.recent_intents:
            recent_intent_types = set(context.recent_intents[-3:])
            if intent in recent_intent_types:
                relevance_score += 0.3
        
        # Time-based relevance
        if context.message_count > 0:
            time_gap = (datetime.now() - context.last_activity).total_seconds()
            if time_gap < 60:  # Within 1 minute
                relevance_score += 0.3
            elif time_gap < 300:  # Within 5 minutes
                relevance_score += 0.2
        
        return min(relevance_score, 1.0)
    
    def _generate_proactive_suggestions(self, session_id: str, intent: str, entities: Dict[str, Any]) -> List[str]:
        """Generate proactive suggestions based on context"""
        suggestions = []
        
        # Intent-based suggestions
        suggestion_map = {
            "file_operation": [
                "Would you like me to backup these files?",
                "Should I organize these files by date or type?"
            ],
            "get_weather": [
                "Would you like weather alerts for tomorrow?",
                "Should I check weather for your commute route?"
            ],
            "calculation": [
                "Would you like me to save this calculation?",
                "Should I create a formula for future use?"
            ],
            "search": [
                "Would you like me to save these search results?",
                "Should I set up alerts for this topic?"
            ]
        }
        
        if intent in suggestion_map:
            suggestions.extend(suggestion_map[intent])
        
        # Context-based suggestions
        if self.context_manager:
            context = self.context_manager.get_or_create_context(session_id)
            
            # Suggest based on active tasks
            if context.active_tasks:
                suggestions.append("Would you like an update on your active tasks?")
            
            # Suggest based on time patterns
            current_hour = datetime.now().hour
            if session_id in self.time_based_activities:
                typical_activities = self.time_based_activities[session_id].get(current_hour, [])
                if typical_activities and intent not in typical_activities:
                    most_common = Counter(typical_activities).most_common(1)[0][0]
                    suggestions.append(f"You usually do {most_common} at this time. Need help with that?")
        
        return suggestions[:2]  # Return top 2 suggestions
    
    def _update_behavior_tracking(self, session_id: str, intent: str, entities: Dict[str, Any], timestamp: datetime):
        """Update behavior tracking data"""
        # Update command sequences
        if session_id not in self.command_sequences:
            self.command_sequences[session_id] = []
        
        # Add to current sequence or start new one
        if (self.command_sequences[session_id] and 
            (timestamp - datetime.now()).total_seconds() < 300):  # Within 5 minutes
            self.command_sequences[session_id][-1].append(intent)
        else:
            self.command_sequences[session_id].append([intent])
        
        # Keep only recent sequences
        self.command_sequences[session_id] = self.command_sequences[session_id][-10:]
        
        # Update topic transitions
        if self.context_manager:
            context = self.context_manager.get_or_create_context(session_id)
            if context.current_topic and context.recent_intents:
                previous_intent = context.recent_intents[-1] if context.recent_intents else None
                if previous_intent:
                    self.topic_transitions[session_id][f"{previous_intent}->{intent}"] += 1
    
    def _generate_contextual_insights(self, session_id: str, analysis: Dict[str, Any]) -> List[ContextualInsight]:
        """Generate new contextual insights"""
        insights = []
        
        # Temporal pattern insights
        temporal_context = analysis.get("temporal_context", {})
        if temporal_context.get("is_unusual_time"):
            insight = ContextualInsight(
                insight_type="temporal",
                description=f"User is active at unusual time: {temporal_context['current_hour']}:00",
                confidence=0.7,
                evidence=[f"Typical activity time differs from current time"],
                metadata=temporal_context
            )
            insights.append(insight)
        
        # Behavioral pattern insights
        patterns = analysis.get("behavioral_patterns", [])
        if patterns:
            insight = ContextualInsight(
                insight_type="behavior",
                description=f"Detected behavioral patterns: {', '.join(patterns[:2])}",
                confidence=0.8,
                evidence=patterns,
                metadata={"pattern_count": len(patterns)}
            )
            insights.append(insight)
        
        # Store insights
        if insights:
            self.contextual_insights[session_id].extend(insights)
            # Keep only recent insights
            self.contextual_insights[session_id] = self.contextual_insights[session_id][-20:]
        
        return insights
    
    def _is_unusual_activity_time(self, session_id: str, intent: str, hour: int, day: str) -> bool:
        """Check if current activity time is unusual for the user"""
        if session_id not in self.time_based_activities:
            return False
        
        hour_activities = self.time_based_activities[session_id].get(hour, [])
        if len(hour_activities) < 3:  # Not enough data
            return False
        
        # Check if current intent is common at this hour
        intent_count = hour_activities.count(intent)
        total_count = len(hour_activities)
        
        # If intent represents less than 20% of activities at this hour, it's unusual
        return (intent_count / total_count) < 0.2
    
    def _matches_pattern_start(self, partial_words: List[str], pattern_words: List[str]) -> bool:
        """Check if partial words match the start of a pattern"""
        if len(partial_words) > len(pattern_words):
            return False
        
        for i, word in enumerate(partial_words):
            if i >= len(pattern_words) or word != pattern_words[i]:
                return False
        
        return True
    
    def _get_time_based_suggestions(self, session_id: str) -> List[Dict[str, Any]]:
        """Get suggestions based on time patterns"""
        suggestions = []
        current_hour = datetime.now().hour
        
        if session_id in self.time_based_activities:
            typical_activities = self.time_based_activities[session_id].get(current_hour, [])
            if typical_activities:
                most_common = Counter(typical_activities).most_common(1)[0]
                suggestions.append({
                    "type": "temporal",
                    "suggestion": f"You usually {most_common[0]} at this time",
                    "confidence": min(most_common[1] / 10.0, 0.9),
                    "action": most_common[0]
                })
        
        return suggestions
    
    def _get_pattern_based_suggestions(self, session_id: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get suggestions based on behavioral patterns"""
        suggestions = []
        
        # Check command sequence patterns
        if session_id in self.command_sequences:
            recent_sequences = self.command_sequences[session_id][-3:]
            if recent_sequences:
                # Find common next commands
                last_commands = [seq[-1] for seq in recent_sequences if seq]
                if last_commands:
                    command_counter = Counter(last_commands)
                    most_common = command_counter.most_common(1)[0]
                    
                    suggestions.append({
                        "type": "sequential",
                        "suggestion": f"Continue with {most_common[0]}",
                        "confidence": min(most_common[1] / 5.0, 0.8),
                        "action": most_common[0]
                    })
        
        return suggestions
    
    def _get_context_aware_suggestions(self, session_id: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get context-aware suggestions"""
        suggestions = []
        
        # Suggest based on current topic
        current_topic = context.get("current_topic")
        if current_topic:
            topic_suggestions = {
                "mathematics": "Would you like to save this calculation?",
                "file_management": "Should I organize these files?",
                "weather": "Would you like weather alerts?",
                "system_management": "Should I monitor system performance?"
            }
            
            if current_topic in topic_suggestions:
                suggestions.append({
                    "type": "contextual",
                    "suggestion": topic_suggestions[current_topic],
                    "confidence": 0.6,
                    "action": f"proactive_{current_topic}"
                })
        
        return suggestions
    
    def _summarize_behavioral_patterns(self, session_id: str) -> Dict[str, Any]:
        """Summarize user behavioral patterns"""
        summary = {
            "command_sequences": [],
            "topic_transitions": {},
            "activity_frequency": {}
        }
        
        # Command sequences
        if session_id in self.command_sequences:
            sequences = self.command_sequences[session_id]
            sequence_counter = Counter(tuple(seq) for seq in sequences if len(seq) > 1)
            summary["command_sequences"] = [
                {"sequence": list(seq), "frequency": count}
                for seq, count in sequence_counter.most_common(5)
            ]
        
        # Topic transitions
        if session_id in self.topic_transitions:
            summary["topic_transitions"] = dict(self.topic_transitions[session_id])
        
        return summary
    
    def _analyze_temporal_preferences(self, session_id: str) -> Dict[str, Any]:
        """Analyze user temporal preferences"""
        preferences = {
            "peak_hours": [],
            "preferred_days": [],
            "activity_distribution": {}
        }
        
        if session_id in self.time_based_activities:
            hour_activities = self.time_based_activities[session_id]
            
            # Find peak hours
            hour_counts = {hour: len(activities) for hour, activities in hour_activities.items()}
            sorted_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)
            preferences["peak_hours"] = [hour for hour, count in sorted_hours[:3]]
            
            # Activity distribution
            all_activities = [activity for activities in hour_activities.values() for activity in activities]
            activity_counter = Counter(all_activities)
            preferences["activity_distribution"] = dict(activity_counter.most_common(10))
        
        return preferences
    
    def _get_usage_statistics(self, session_id: str) -> Dict[str, Any]:
        """Get user usage statistics"""
        stats = {
            "total_interactions": 0,
            "unique_intents": 0,
            "average_session_length": 0,
            "most_used_features": []
        }
        
        # Calculate from available data
        if session_id in self.time_based_activities:
            all_activities = [
                activity for activities in self.time_based_activities[session_id].values() 
                for activity in activities
            ]
            stats["total_interactions"] = len(all_activities)
            stats["unique_intents"] = len(set(all_activities))
            
            activity_counter = Counter(all_activities)
            stats["most_used_features"] = [
                {"feature": feature, "count": count}
                for feature, count in activity_counter.most_common(5)
            ]
        
        return stats
    
    def _generate_personalization_recommendations(self, session_id: str) -> List[str]:
        """Generate personalization recommendations"""
        recommendations = []
        
        # Analyze user patterns to suggest improvements
        if session_id in self.contextual_insights:
            insights = self.contextual_insights[session_id]
            
            # Temporal recommendations
            temporal_insights = [i for i in insights if i.insight_type == "temporal"]
            if temporal_insights:
                recommendations.append("Consider setting up automated tasks for your peak activity hours")
            
            # Behavioral recommendations
            behavioral_insights = [i for i in insights if i.insight_type == "behavior"]
            if behavioral_insights:
                recommendations.append("I can create shortcuts for your common command sequences")
        
        # Usage-based recommendations
        stats = self._get_usage_statistics(session_id)
        if stats["most_used_features"]:
            top_feature = stats["most_used_features"][0]["feature"]
            recommendations.append(f"Consider advanced features for {top_feature} to improve efficiency")
        
        return recommendations[:3]
