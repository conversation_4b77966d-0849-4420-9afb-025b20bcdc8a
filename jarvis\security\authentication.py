"""
Authentication System for JARVIS AI Assistant
"""

import hashlib
import secrets
import logging
from typing import Dict, Optional, Tuple, List
from datetime import datetime, timedelta
import json
from pathlib import Path
from dataclasses import dataclass, field

# Optional imports with fallbacks
try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False

from ..config import config

logger = logging.getLogger(__name__)


@dataclass
class User:
    """User account representation"""
    user_id: str
    username: str
    email: Optional[str] = None
    password_hash: str = ""
    salt: str = ""
    is_active: bool = True
    is_admin: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    permissions: List[str] = field(default_factory=list)
    metadata: Dict[str, any] = field(default_factory=dict)


@dataclass
class AuthToken:
    """Authentication token representation"""
    token_id: str
    user_id: str
    token_type: str  # access, refresh, api
    expires_at: datetime
    created_at: datetime = field(default_factory=datetime.now)
    is_revoked: bool = False
    permissions: List[str] = field(default_factory=list)
    metadata: Dict[str, any] = field(default_factory=dict)


class AuthenticationManager:
    """Comprehensive authentication management system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize authentication manager"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.users_file = self.data_dir / "users.json"
        self.tokens_file = self.data_dir / "tokens.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Security settings
        self.jwt_secret = config.get("security.jwt_secret", self._generate_jwt_secret())
        self.token_expiry_hours = config.get("security.token_expiry_hours", 24)
        self.max_failed_attempts = config.get("security.max_failed_attempts", 5)
        self.lockout_duration_minutes = config.get("security.lockout_duration_minutes", 30)
        self.password_min_length = config.get("security.password_min_length", 8)
        
        # In-memory storage
        self.users: Dict[str, User] = {}
        self.active_tokens: Dict[str, AuthToken] = {}
        
        # Load existing data
        self._load_users()
        self._load_tokens()
        
        # Create default admin user if none exists
        self._ensure_admin_user()
        
        logger.info("Authentication manager initialized")
    
    def create_user(
        self,
        username: str,
        password: str,
        email: Optional[str] = None,
        is_admin: bool = False,
        permissions: Optional[List[str]] = None
    ) -> Tuple[bool, str]:
        """Create a new user account"""
        try:
            # Validate input
            if not username or not password:
                return False, "Username and password are required"
            
            if username in [user.username for user in self.users.values()]:
                return False, "Username already exists"
            
            if not self._validate_password(password):
                return False, f"Password must be at least {self.password_min_length} characters"
            
            # Generate user ID and salt
            user_id = secrets.token_urlsafe(16)
            salt = secrets.token_urlsafe(32)
            
            # Hash password
            password_hash = self._hash_password(password, salt)
            
            # Create user
            user = User(
                user_id=user_id,
                username=username,
                email=email,
                password_hash=password_hash,
                salt=salt,
                is_admin=is_admin,
                permissions=permissions or self._get_default_permissions()
            )
            
            self.users[user_id] = user
            self._save_users()
            
            logger.info(f"Created user: {username}")
            return True, user_id
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return False, str(e)
    
    def authenticate_user(self, username: str, password: str) -> Tuple[bool, Optional[str], str]:
        """Authenticate user credentials"""
        try:
            # Find user by username
            user = None
            for u in self.users.values():
                if u.username == username:
                    user = u
                    break
            
            if not user:
                return False, None, "Invalid username or password"
            
            # Check if account is locked
            if user.locked_until and datetime.now() < user.locked_until:
                remaining_minutes = int((user.locked_until - datetime.now()).total_seconds() / 60)
                return False, None, f"Account locked for {remaining_minutes} more minutes"
            
            # Check if account is active
            if not user.is_active:
                return False, None, "Account is disabled"
            
            # Verify password
            if not self._verify_password(password, user.password_hash, user.salt):
                # Increment failed attempts
                user.failed_login_attempts += 1
                
                if user.failed_login_attempts >= self.max_failed_attempts:
                    user.locked_until = datetime.now() + timedelta(minutes=self.lockout_duration_minutes)
                    logger.warning(f"Account locked for user: {username}")
                
                self._save_users()
                return False, None, "Invalid username or password"
            
            # Successful authentication
            user.last_login = datetime.now()
            user.failed_login_attempts = 0
            user.locked_until = None
            self._save_users()
            
            logger.info(f"User authenticated: {username}")
            return True, user.user_id, "Authentication successful"
            
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            return False, None, "Authentication error"
    
    def create_token(
        self,
        user_id: str,
        token_type: str = "access",
        expiry_hours: Optional[int] = None
    ) -> Tuple[bool, Optional[str], str]:
        """Create authentication token"""
        try:
            if user_id not in self.users:
                return False, None, "User not found"
            
            user = self.users[user_id]
            
            # Set expiry
            expiry_hours = expiry_hours or self.token_expiry_hours
            expires_at = datetime.now() + timedelta(hours=expiry_hours)
            
            # Create token payload
            payload = {
                "user_id": user_id,
                "username": user.username,
                "token_type": token_type,
                "permissions": user.permissions,
                "is_admin": user.is_admin,
                "exp": expires_at.timestamp(),
                "iat": datetime.now().timestamp(),
                "jti": secrets.token_urlsafe(16)  # JWT ID
            }
            
            # Generate JWT token
            if JWT_AVAILABLE:
                token = jwt.encode(payload, self.jwt_secret, algorithm="HS256")
            else:
                # Fallback to simple token if JWT not available
                import base64
                token_data = json.dumps(payload)
                token = base64.b64encode(token_data.encode()).decode()
            
            # Store token info
            token_info = AuthToken(
                token_id=payload["jti"],
                user_id=user_id,
                token_type=token_type,
                expires_at=expires_at,
                permissions=user.permissions
            )
            
            self.active_tokens[payload["jti"]] = token_info
            self._save_tokens()
            
            logger.info(f"Created {token_type} token for user: {user.username}")
            return True, token, "Token created successfully"
            
        except Exception as e:
            logger.error(f"Error creating token: {e}")
            return False, None, str(e)
    
    def validate_token(self, token: str) -> Tuple[bool, Optional[Dict], str]:
        """Validate authentication token"""
        try:
            # Decode JWT token
            if JWT_AVAILABLE:
                payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            else:
                # Fallback decoding for simple tokens
                import base64
                token_data = base64.b64decode(token.encode()).decode()
                payload = json.loads(token_data)

                # Check expiry manually for fallback tokens
                if "exp" in payload and payload["exp"] < datetime.now().timestamp():
                    return False, None, "Token has expired"
            
            token_id = payload.get("jti")
            user_id = payload.get("user_id")
            
            # Check if token exists and is not revoked
            if token_id not in self.active_tokens:
                return False, None, "Token not found"
            
            token_info = self.active_tokens[token_id]
            if token_info.is_revoked:
                return False, None, "Token has been revoked"
            
            # Check if user still exists and is active
            if user_id not in self.users:
                return False, None, "User not found"
            
            user = self.users[user_id]
            if not user.is_active:
                return False, None, "User account is disabled"
            
            # Token is valid
            return True, payload, "Token is valid"
            
        except Exception as e:
            if JWT_AVAILABLE:
                if "ExpiredSignatureError" in str(type(e)):
                    return False, None, "Token has expired"
                elif "InvalidTokenError" in str(type(e)):
                    return False, None, "Invalid token"
            return False, None, "Token validation error"
        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return False, None, "Token validation error"
    
    def revoke_token(self, token: str) -> Tuple[bool, str]:
        """Revoke authentication token"""
        try:
            # Decode token to get token ID
            if JWT_AVAILABLE:
                payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            else:
                import base64
                token_data = base64.b64decode(token.encode()).decode()
                payload = json.loads(token_data)

            token_id = payload.get("jti")
            
            if token_id in self.active_tokens:
                self.active_tokens[token_id].is_revoked = True
                self._save_tokens()
                
                logger.info(f"Revoked token: {token_id}")
                return True, "Token revoked successfully"
            
            return False, "Token not found"
            
        except Exception as e:
            logger.error(f"Error revoking token: {e}")
            return False, str(e)
    
    def change_password(
        self,
        user_id: str,
        old_password: str,
        new_password: str
    ) -> Tuple[bool, str]:
        """Change user password"""
        try:
            if user_id not in self.users:
                return False, "User not found"
            
            user = self.users[user_id]
            
            # Verify old password
            if not self._verify_password(old_password, user.password_hash, user.salt):
                return False, "Current password is incorrect"
            
            # Validate new password
            if not self._validate_password(new_password):
                return False, f"New password must be at least {self.password_min_length} characters"
            
            # Generate new salt and hash
            new_salt = secrets.token_urlsafe(32)
            new_password_hash = self._hash_password(new_password, new_salt)
            
            # Update user
            user.password_hash = new_password_hash
            user.salt = new_salt
            self._save_users()
            
            logger.info(f"Password changed for user: {user.username}")
            return True, "Password changed successfully"
            
        except Exception as e:
            logger.error(f"Error changing password: {e}")
            return False, str(e)
    
    def get_user_info(self, user_id: str) -> Optional[Dict]:
        """Get user information (without sensitive data)"""
        if user_id not in self.users:
            return None
        
        user = self.users[user_id]
        return {
            "user_id": user.user_id,
            "username": user.username,
            "email": user.email,
            "is_active": user.is_active,
            "is_admin": user.is_admin,
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "permissions": user.permissions
        }
    
    def list_users(self, admin_user_id: str) -> List[Dict]:
        """List all users (admin only)"""
        if admin_user_id not in self.users or not self.users[admin_user_id].is_admin:
            return []
        
        return [
            {
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "is_active": user.is_active,
                "is_admin": user.is_admin,
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "failed_attempts": user.failed_login_attempts,
                "locked_until": user.locked_until.isoformat() if user.locked_until else None
            }
            for user in self.users.values()
        ]
    
    def cleanup_expired_tokens(self):
        """Clean up expired tokens"""
        now = datetime.now()
        expired_tokens = []
        
        for token_id, token_info in self.active_tokens.items():
            if token_info.expires_at < now:
                expired_tokens.append(token_id)
        
        for token_id in expired_tokens:
            del self.active_tokens[token_id]
        
        if expired_tokens:
            self._save_tokens()
            logger.info(f"Cleaned up {len(expired_tokens)} expired tokens")
    
    def get_auth_stats(self) -> Dict[str, any]:
        """Get authentication statistics"""
        now = datetime.now()
        
        return {
            "total_users": len(self.users),
            "active_users": sum(1 for user in self.users.values() if user.is_active),
            "admin_users": sum(1 for user in self.users.values() if user.is_admin),
            "locked_users": sum(1 for user in self.users.values() if user.locked_until and user.locked_until > now),
            "active_tokens": len([t for t in self.active_tokens.values() if not t.is_revoked and t.expires_at > now]),
            "total_tokens": len(self.active_tokens),
            "expired_tokens": len([t for t in self.active_tokens.values() if t.expires_at < now])
        }
    
    # Helper methods
    def _generate_jwt_secret(self) -> str:
        """Generate JWT secret key"""
        secret = secrets.token_urlsafe(64)
        # Save to config for persistence
        config.set("security.jwt_secret", secret)
        return secret
    
    def _hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt"""
        return hashlib.pbkdf2_hex(password.encode(), salt.encode(), 100000)
    
    def _verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        return self._hash_password(password, salt) == password_hash
    
    def _validate_password(self, password: str) -> bool:
        """Validate password strength"""
        if len(password) < self.password_min_length:
            return False
        
        # Add more password strength checks here
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        
        return has_upper and has_lower and has_digit
    
    def _get_default_permissions(self) -> List[str]:
        """Get default permissions for new users"""
        return config.get("security.default_permissions", [
            "basic_commands",
            "file_read",
            "web_search",
            "system_info"
        ])
    
    def _ensure_admin_user(self):
        """Ensure at least one admin user exists"""
        admin_users = [user for user in self.users.values() if user.is_admin]
        
        if not admin_users:
            # Create default admin user
            admin_username = config.get("security.default_admin_username", "admin")
            admin_password = config.get("security.default_admin_password", "jarvis123")
            
            success, user_id = self.create_user(
                username=admin_username,
                password=admin_password,
                is_admin=True,
                permissions=["*"]  # All permissions
            )
            
            if success:
                logger.info(f"Created default admin user: {admin_username}")
            else:
                logger.error("Failed to create default admin user")
    
    def _load_users(self):
        """Load users from file"""
        try:
            if self.users_file.exists():
                with open(self.users_file, 'r') as f:
                    users_data = json.load(f)
                
                for user_id, user_dict in users_data.items():
                    # Convert datetime strings back to datetime objects
                    user_dict["created_at"] = datetime.fromisoformat(user_dict["created_at"])
                    if user_dict.get("last_login"):
                        user_dict["last_login"] = datetime.fromisoformat(user_dict["last_login"])
                    if user_dict.get("locked_until"):
                        user_dict["locked_until"] = datetime.fromisoformat(user_dict["locked_until"])
                    
                    user = User(**user_dict)
                    self.users[user_id] = user
                
                logger.info(f"Loaded {len(self.users)} users")
                
        except Exception as e:
            logger.error(f"Error loading users: {e}")
    
    def _save_users(self):
        """Save users to file"""
        try:
            users_data = {}
            
            for user_id, user in self.users.items():
                user_dict = {
                    "user_id": user.user_id,
                    "username": user.username,
                    "email": user.email,
                    "password_hash": user.password_hash,
                    "salt": user.salt,
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "created_at": user.created_at.isoformat(),
                    "last_login": user.last_login.isoformat() if user.last_login else None,
                    "failed_login_attempts": user.failed_login_attempts,
                    "locked_until": user.locked_until.isoformat() if user.locked_until else None,
                    "permissions": user.permissions,
                    "metadata": user.metadata
                }
                users_data[user_id] = user_dict
            
            with open(self.users_file, 'w') as f:
                json.dump(users_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving users: {e}")
    
    def _load_tokens(self):
        """Load tokens from file"""
        try:
            if self.tokens_file.exists():
                with open(self.tokens_file, 'r') as f:
                    tokens_data = json.load(f)
                
                for token_id, token_dict in tokens_data.items():
                    # Convert datetime strings back to datetime objects
                    token_dict["expires_at"] = datetime.fromisoformat(token_dict["expires_at"])
                    token_dict["created_at"] = datetime.fromisoformat(token_dict["created_at"])
                    
                    token = AuthToken(**token_dict)
                    self.active_tokens[token_id] = token
                
                logger.info(f"Loaded {len(self.active_tokens)} tokens")
                
        except Exception as e:
            logger.error(f"Error loading tokens: {e}")
    
    def _save_tokens(self):
        """Save tokens to file"""
        try:
            tokens_data = {}
            
            for token_id, token in self.active_tokens.items():
                token_dict = {
                    "token_id": token.token_id,
                    "user_id": token.user_id,
                    "token_type": token.token_type,
                    "expires_at": token.expires_at.isoformat(),
                    "created_at": token.created_at.isoformat(),
                    "is_revoked": token.is_revoked,
                    "permissions": token.permissions,
                    "metadata": token.metadata
                }
                tokens_data[token_id] = token_dict
            
            with open(self.tokens_file, 'w') as f:
                json.dump(tokens_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving tokens: {e}")
