"""
Voice Interface for JARVIS AI Assistant
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import re

from .speech_recognition import Speech<PERSON>ecognizer
from .text_to_speech import TextToSpeech
from ...config import config

logger = logging.getLogger(__name__)


class VoiceInterface:
    """Integrated voice interface with speech recognition and text-to-speech"""
    
    def __init__(self, message_callback: Optional[Callable] = None):
        """
        Initialize voice interface
        
        Args:
            message_callback: Callback function for processing recognized speech
        """
        self.speech_recognizer = SpeechRecognizer()
        self.text_to_speech = TextToSpeech()
        self.message_callback = message_callback
        
        # Voice activation settings
        self.wake_words = config.get("voice.wake_words", ["jarvis", "hey jarvis", "computer"])
        self.listening = False
        self.continuous_mode = False
        self.voice_activation_enabled = config.get("voice.activation.enabled", True)
        self.activation_timeout = config.get("voice.activation.timeout", 10.0)
        
        # Voice personality settings
        self.personality = config.get("voice.personality", "professional")
        self.response_style = config.get("voice.response_style", "concise")
        
        logger.info("Voice interface initialized")
    
    async def start_listening(self, continuous: bool = False) -> Dict[str, Any]:
        """
        Start voice listening mode
        
        Args:
            continuous: Enable continuous listening mode
            
        Returns:
            Dictionary with operation result
        """
        try:
            self.listening = True
            self.continuous_mode = continuous
            
            if continuous:
                logger.info("Starting continuous voice listening...")
                return await self._continuous_listening_loop()
            else:
                logger.info("Starting single voice command...")
                return await self._single_command_listening()
                
        except Exception as e:
            logger.error(f"Error starting voice listening: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stop_listening(self) -> Dict[str, Any]:
        """Stop voice listening"""
        try:
            self.listening = False
            self.continuous_mode = False
            
            logger.info("Voice listening stopped")
            return {
                "success": True,
                "message": "Voice listening stopped"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _continuous_listening_loop(self) -> Dict[str, Any]:
        """Continuous listening loop with wake word detection"""
        try:
            wake_word_detected = False
            
            while self.listening:
                if self.voice_activation_enabled and not wake_word_detected:
                    # Listen for wake word
                    result = await self.speech_recognizer.recognize_from_microphone(duration=3.0)
                    
                    if result["success"] and result.get("text"):
                        text = result["text"].lower().strip()
                        
                        # Check for wake words
                        for wake_word in self.wake_words:
                            if wake_word.lower() in text:
                                wake_word_detected = True
                                await self.speak("Yes, I'm listening.")
                                logger.info(f"Wake word detected: {wake_word}")
                                break
                else:
                    # Listen for actual command
                    result = await self.speech_recognizer.recognize_from_microphone(
                        duration=self.activation_timeout
                    )
                    
                    if result["success"] and result.get("text"):
                        text = result["text"].strip()
                        
                        if text:
                            # Process the command
                            await self._process_voice_command(text)
                            
                            # Reset wake word detection
                            if self.voice_activation_enabled:
                                wake_word_detected = False
                                await self.speak("Listening for wake word...")
                    else:
                        # Timeout or no speech detected
                        if wake_word_detected:
                            await self.speak("I didn't hear anything. Say the wake word to activate me again.")
                            wake_word_detected = False
                
                # Short pause between listening cycles
                await asyncio.sleep(0.5)
            
            return {
                "success": True,
                "message": "Continuous listening completed"
            }
            
        except Exception as e:
            logger.error(f"Error in continuous listening: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _single_command_listening(self) -> Dict[str, Any]:
        """Listen for a single voice command"""
        try:
            await self.speak("I'm listening...")
            
            result = await self.speech_recognizer.recognize_from_microphone(
                duration=self.activation_timeout
            )
            
            if result["success"] and result.get("text"):
                text = result["text"].strip()
                if text:
                    await self._process_voice_command(text)
                    return {
                        "success": True,
                        "text": text,
                        "message": "Command processed"
                    }
            
            await self.speak("I didn't hear anything.")
            return {
                "success": False,
                "error": "No speech detected"
            }
            
        except Exception as e:
            logger.error(f"Error in single command listening: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _process_voice_command(self, text: str) -> None:
        """Process recognized voice command"""
        try:
            logger.info(f"Processing voice command: {text}")
            
            # Check for voice interface commands first
            if await self._handle_voice_commands(text):
                return
            
            # Pass to main message callback if available
            if self.message_callback:
                response = await self.message_callback(text)
                if response:
                    await self.speak(response)
            else:
                await self.speak("I heard you say: " + text)
                
        except Exception as e:
            logger.error(f"Error processing voice command: {e}")
            await self.speak("Sorry, I had trouble processing that command.")
    
    async def _handle_voice_commands(self, text: str) -> bool:
        """Handle voice interface specific commands"""
        text_lower = text.lower().strip()
        
        # Stop listening commands
        if any(phrase in text_lower for phrase in ["stop listening", "stop voice", "quiet", "silence"]):
            await self.speak("Stopping voice interface.")
            await self.stop_listening()
            return True
        
        # Voice settings commands
        if "change voice" in text_lower or "switch voice" in text_lower:
            await self._handle_voice_change_command(text)
            return True
        
        # Volume commands
        if any(phrase in text_lower for phrase in ["speak louder", "speak softer", "volume"]):
            await self._handle_volume_command(text)
            return True
        
        # Repeat commands
        if any(phrase in text_lower for phrase in ["repeat", "say that again", "what did you say"]):
            await self.speak("I'm sorry, I don't have a previous message to repeat.")
            return True
        
        # Status commands
        if any(phrase in text_lower for phrase in ["voice status", "voice info", "voice settings"]):
            await self._handle_voice_status_command()
            return True
        
        return False
    
    async def _handle_voice_change_command(self, text: str) -> None:
        """Handle voice change commands"""
        try:
            voices_result = await self.text_to_speech.get_voices()
            
            if voices_result["success"]:
                voices = voices_result["voices"]
                if voices:
                    # For now, just switch to the next available voice
                    current_voice = self.text_to_speech.voice_id
                    voice_ids = [v["id"] for v in voices]
                    
                    try:
                        current_index = voice_ids.index(current_voice)
                        next_index = (current_index + 1) % len(voice_ids)
                        new_voice = voice_ids[next_index]
                    except ValueError:
                        new_voice = voice_ids[0]
                    
                    if self.text_to_speech.set_voice(new_voice):
                        await self.speak(f"Voice changed to {voices[voice_ids.index(new_voice)]['name']}")
                    else:
                        await self.speak("Sorry, I couldn't change the voice.")
                else:
                    await self.speak("No alternative voices available.")
            else:
                await self.speak("Sorry, I couldn't access voice settings.")
                
        except Exception as e:
            logger.error(f"Error changing voice: {e}")
            await self.speak("Sorry, I had trouble changing the voice.")
    
    async def _handle_volume_command(self, text: str) -> None:
        """Handle volume commands"""
        # Note: Volume control would depend on the TTS provider and system
        await self.speak("Volume control is not yet implemented.")
    
    async def _handle_voice_status_command(self) -> None:
        """Handle voice status commands"""
        try:
            stt_stats = self.speech_recognizer.get_stats()
            tts_stats = self.text_to_speech.get_stats()
            
            status_message = f"Voice interface status: Speech recognition using {stt_stats['provider']}, "
            status_message += f"text to speech using {tts_stats['provider']}. "
            status_message += f"Continuous mode is {'enabled' if self.continuous_mode else 'disabled'}."
            
            await self.speak(status_message)
            
        except Exception as e:
            logger.error(f"Error getting voice status: {e}")
            await self.speak("Sorry, I couldn't get the voice status.")
    
    async def speak(self, text: str, save_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert text to speech with personality adjustments
        
        Args:
            text: Text to speak
            save_file: Optional file to save audio
            
        Returns:
            Dictionary with operation result
        """
        try:
            # Apply personality adjustments
            adjusted_text = self._apply_personality(text)
            
            # Use text-to-speech
            result = await self.text_to_speech.speak(adjusted_text, save_file)
            
            if result["success"]:
                logger.info(f"Spoke: {adjusted_text}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in speak: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _apply_personality(self, text: str) -> str:
        """Apply personality adjustments to text"""
        try:
            # Apply response style
            if self.response_style == "concise":
                # Keep text as is for concise style
                pass
            elif self.response_style == "detailed":
                # Add more context for detailed style
                if not text.endswith(('.', '!', '?')):
                    text += "."
            elif self.response_style == "casual":
                # Make text more casual
                text = text.replace("I am", "I'm")
                text = text.replace("cannot", "can't")
                text = text.replace("will not", "won't")
            
            # Apply personality traits
            if self.personality == "friendly":
                if not any(greeting in text.lower() for greeting in ["hi", "hello", "hey"]):
                    if text and not text[0].islower():
                        text = "Sure! " + text
            elif self.personality == "professional":
                # Keep formal tone
                pass
            elif self.personality == "enthusiastic":
                if not text.endswith('!'):
                    text = text.rstrip('.') + "!"
            
            return text
            
        except Exception as e:
            logger.error(f"Error applying personality: {e}")
            return text
    
    async def test_voice_interface(self) -> Dict[str, Any]:
        """Test the voice interface components"""
        try:
            results = {
                "speech_recognition": {},
                "text_to_speech": {},
                "overall": {}
            }
            
            # Test speech recognition
            stt_providers = self.speech_recognizer.get_available_providers()
            results["speech_recognition"] = {
                "available_providers": stt_providers,
                "current_provider": self.speech_recognizer.provider,
                "working": len(stt_providers) > 0
            }
            
            # Test text-to-speech
            tts_providers = self.text_to_speech.get_available_providers()
            results["text_to_speech"] = {
                "available_providers": tts_providers,
                "current_provider": self.text_to_speech.provider,
                "working": len(tts_providers) > 0
            }
            
            # Test TTS with a simple message
            if tts_providers:
                tts_result = await self.speak("Voice interface test successful.")
                results["text_to_speech"]["test_result"] = tts_result["success"]
            
            # Overall status
            results["overall"] = {
                "voice_interface_ready": len(stt_providers) > 0 and len(tts_providers) > 0,
                "wake_words": self.wake_words,
                "voice_activation_enabled": self.voice_activation_enabled
            }
            
            return {
                "success": True,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error testing voice interface: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get voice interface statistics"""
        return {
            "listening": self.listening,
            "continuous_mode": self.continuous_mode,
            "voice_activation_enabled": self.voice_activation_enabled,
            "wake_words": self.wake_words,
            "personality": self.personality,
            "response_style": self.response_style,
            "speech_recognition": self.speech_recognizer.get_stats(),
            "text_to_speech": self.text_to_speech.get_stats()
        }
