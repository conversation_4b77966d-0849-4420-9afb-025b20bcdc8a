"""
Web Scraper for JARVIS AI Assistant
"""

import re
import logging
from typing import Dict, Any
from urllib.parse import urljoin, urlparse
import requests
from datetime import datetime

logger = logging.getLogger(__name__)


class WebScraper:
    """Advanced web scraper for content extraction"""
    
    def __init__(self):
        """Initialize web scraper"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        logger.info("Web scraper initialized")
    
    def extract_content(self, url: str, content_type: str = "auto") -> Dict[str, Any]:
        """
        Extract content from web page
        
        Args:
            url: URL to scrape
            content_type: Type of content to extract (auto, text, links, images, etc.)
            
        Returns:
            Dictionary with extracted content
        """
        try:
            # Get page content
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Try to import BeautifulSoup
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                if content_type == "auto":
                    return self._extract_all_content(url, soup, response)
                elif content_type == "text":
                    return self._extract_text(url, soup)
                elif content_type == "links":
                    return self._extract_links(url, soup)
                elif content_type == "images":
                    return self._extract_images(url, soup)
                elif content_type == "metadata":
                    return self._extract_metadata(url, soup)
                else:
                    return self._extract_all_content(url, soup, response)
                    
            except ImportError:
                # Fallback to basic text extraction without BeautifulSoup
                return self._extract_basic_content(url, response.text)
                
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _extract_all_content(self, url: str, soup, response) -> Dict[str, Any]:
        """Extract comprehensive content from page"""
        try:
            # Basic page info
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No title"
            
            # Meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            description = meta_desc.get('content', '') if meta_desc else ''
            
            # Extract main text content
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            # Get text content
            text_content = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text_content = ' '.join(chunk for chunk in chunks if chunk)
            
            # Extract links
            links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if href.startswith('http') or href.startswith('/'):
                    full_url = urljoin(url, href)
                    links.append({
                        "text": link.get_text().strip(),
                        "url": full_url
                    })
            
            # Extract images
            images = []
            for img in soup.find_all('img', src=True):
                src = img['src']
                full_url = urljoin(url, src)
                images.append({
                    "src": full_url,
                    "alt": img.get('alt', ''),
                    "title": img.get('title', '')
                })
            
            # Extract headings
            headings = []
            for i in range(1, 7):
                for heading in soup.find_all(f'h{i}'):
                    headings.append({
                        "level": i,
                        "text": heading.get_text().strip()
                    })
            
            return {
                "success": True,
                "url": url,
                "title": title_text,
                "description": description,
                "text_content": text_content,
                "word_count": len(text_content.split()),
                "links": links[:20],  # Limit to first 20 links
                "images": images[:10],  # Limit to first 10 images
                "headings": headings,
                "content_length": len(text_content),
                "status_code": response.status_code,
                "content_type": response.headers.get('Content-Type', ''),
                "extracted_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _extract_text(self, url: str, soup) -> Dict[str, Any]:
        """Extract only text content"""
        try:
            # Remove unwanted elements
            for element in soup(["script", "style", "nav", "footer", "header", "aside"]):
                element.decompose()
            
            # Get main content areas
            main_content = soup.find('main') or soup.find('article') or soup.find('div', class_=re.compile(r'content|main|article'))
            
            if main_content:
                text = main_content.get_text()
            else:
                text = soup.get_text()
            
            # Clean text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            clean_text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Extract paragraphs
            paragraphs = []
            for p in soup.find_all('p'):
                p_text = p.get_text().strip()
                if len(p_text) > 20:  # Only include substantial paragraphs
                    paragraphs.append(p_text)
            
            return {
                "success": True,
                "url": url,
                "text": clean_text,
                "paragraphs": paragraphs,
                "word_count": len(clean_text.split()),
                "character_count": len(clean_text)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _extract_links(self, url: str, soup) -> Dict[str, Any]:
        """Extract all links from page"""
        try:
            links = []
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                text = link.get_text().strip()
                
                # Skip empty links
                if not href or not text:
                    continue
                
                # Convert relative URLs to absolute
                if href.startswith('/'):
                    full_url = urljoin(url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue
                
                # Categorize links
                domain = urlparse(full_url).netloc
                is_external = domain != urlparse(url).netloc
                
                links.append({
                    "text": text,
                    "url": full_url,
                    "domain": domain,
                    "is_external": is_external,
                    "title": link.get('title', '')
                })
            
            # Group by domain
            domains = {}
            for link in links:
                domain = link['domain']
                if domain not in domains:
                    domains[domain] = []
                domains[domain].append(link)
            
            return {
                "success": True,
                "url": url,
                "links": links,
                "total_links": len(links),
                "external_links": len([l for l in links if l['is_external']]),
                "internal_links": len([l for l in links if not l['is_external']]),
                "domains": domains
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _extract_images(self, url: str, soup) -> Dict[str, Any]:
        """Extract all images from page"""
        try:
            images = []
            
            for img in soup.find_all('img'):
                src = img.get('src')
                if not src:
                    continue
                
                # Convert relative URLs to absolute
                if src.startswith('/'):
                    full_url = urljoin(url, src)
                elif src.startswith('http'):
                    full_url = src
                else:
                    full_url = urljoin(url, src)
                
                images.append({
                    "src": full_url,
                    "alt": img.get('alt', ''),
                    "title": img.get('title', ''),
                    "width": img.get('width'),
                    "height": img.get('height'),
                    "class": img.get('class', [])
                })
            
            return {
                "success": True,
                "url": url,
                "images": images,
                "total_images": len(images)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _extract_metadata(self, url: str, soup) -> Dict[str, Any]:
        """Extract page metadata"""
        try:
            metadata = {}
            
            # Title
            title = soup.find('title')
            if title:
                metadata['title'] = title.get_text().strip()
            
            # Meta tags
            for meta in soup.find_all('meta'):
                name = meta.get('name') or meta.get('property')
                content = meta.get('content')
                
                if name and content:
                    metadata[name] = content
            
            # Open Graph data
            og_data = {}
            for meta in soup.find_all('meta', property=re.compile(r'^og:')):
                property_name = meta.get('property')
                content = meta.get('content')
                if property_name and content:
                    og_data[property_name] = content
            
            if og_data:
                metadata['open_graph'] = og_data
            
            # Twitter Card data
            twitter_data = {}
            for meta in soup.find_all('meta', attrs={'name': re.compile(r'^twitter:')}):
                name = meta.get('name')
                content = meta.get('content')
                if name and content:
                    twitter_data[name] = content
            
            if twitter_data:
                metadata['twitter_card'] = twitter_data
            
            # Canonical URL
            canonical = soup.find('link', rel='canonical')
            if canonical:
                metadata['canonical_url'] = canonical.get('href')
            
            return {
                "success": True,
                "url": url,
                "metadata": metadata
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def _extract_basic_content(self, url: str, html_content: str) -> Dict[str, Any]:
        """Basic content extraction without BeautifulSoup"""
        try:
            # Extract title using regex
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
            title = title_match.group(1).strip() if title_match else "No title"
            
            # Remove HTML tags for basic text extraction
            text_content = re.sub(r'<[^>]+>', ' ', html_content)
            text_content = re.sub(r'\s+', ' ', text_content).strip()
            
            return {
                "success": True,
                "url": url,
                "title": title,
                "text_content": text_content[:5000],  # Limit to first 5000 chars
                "word_count": len(text_content.split()),
                "method": "basic_regex",
                "note": "Limited extraction - install BeautifulSoup for full features"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def extract_article(self, url: str) -> Dict[str, Any]:
        """Extract article content using readability-style extraction"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Try to find article content
                article_selectors = [
                    'article',
                    '[role="main"]',
                    '.article-content',
                    '.post-content',
                    '.entry-content',
                    '.content',
                    'main'
                ]
                
                article_content = None
                for selector in article_selectors:
                    article_content = soup.select_one(selector)
                    if article_content:
                        break
                
                if not article_content:
                    # Fallback to body
                    article_content = soup.find('body')
                
                if article_content and hasattr(article_content, 'find_all') and hasattr(article_content, 'name'):
                    # Remove unwanted elements (only if article_content is a Tag object)
                    try:
                        # Use getattr to safely call find_all
                        find_all_method = getattr(article_content, 'find_all', None)
                        if find_all_method and callable(find_all_method):
                            unwanted_elements = find_all_method(["script", "style", "nav", "footer", "header", "aside", "advertisement"])
                        else:
                            unwanted_elements = []
                    except (AttributeError, TypeError):
                        unwanted_elements = []

                    # Ensure unwanted_elements is iterable
                    if unwanted_elements is None or not hasattr(unwanted_elements, '__iter__'):
                        unwanted_elements = []

                    for element in unwanted_elements: # type: ignore
                        if hasattr(element, 'decompose'):
                            element.decompose()
                    
                    # Extract text
                    text = article_content.get_text()
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    clean_text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    # Extract title
                    title = soup.find('title')
                    title_text = title.get_text().strip() if title else "No title"
                    
                    return {
                        "success": True,
                        "url": url,
                        "title": title_text,
                        "content": clean_text,
                        "word_count": len(clean_text.split()),
                        "extracted_at": datetime.now().isoformat()
                    }
                
                return {
                    "success": False,
                    "error": "Could not find article content",
                    "url": url
                }
                
            except ImportError:
                return {
                    "success": False,
                    "error": "BeautifulSoup required for article extraction",
                    "url": url
                }
                
        except Exception as e:
            logger.error(f"Error extracting article from {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get scraper statistics"""
        return {
            "session_cookies": len(self.session.cookies),
            "user_agent": self.session.headers.get('User-Agent', '')
        }
