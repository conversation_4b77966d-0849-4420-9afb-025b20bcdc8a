# 🚀 JARVIS AI Assistant - Quick Start Guide

Get your JARVIS AI Assistant up and running in minutes!

## 📋 Prerequisites

- **Python 3.9+** (Check with `python --version`)
- **Git** (for cloning the repository)
- **Internet connection** (for installing dependencies)

## ⚡ Quick Setup

### 1. <PERSON><PERSON> and Setup
```bash
# Clone the repository
git clone <repository-url>
cd jarvis

# Run the automated setup
python setup.py
```

### 2. Start JARVIS
```bash
# Windows
.\venv\Scripts\python.exe -m jarvis

# macOS/Linux
./venv/bin/python -m jarvis
```

That's it! JARVIS will start in test mode and you can begin chatting immediately.

## 🔑 Adding API Keys (Optional but Recommended)

For full AI capabilities, add your API keys:

### Option 1: Edit Configuration File
```bash
# Edit the config file
notepad jarvis/config/config.yaml  # Windows
nano jarvis/config/config.yaml     # Linux/macOS
```

Add your OpenAI API key:
```yaml
ai:
  openai:
    api_key: "your-openai-api-key-here"
```

### Option 2: Use Environment Variables
```bash
# Edit the .env file
notepad .env  # Windows
nano .env     # Linux/macOS
```

Add:
```
OPENAI_API_KEY=your-openai-api-key-here
```

## 🎯 Basic Usage

### CLI Commands
- `/help` - Show available commands
- `/status` - Check system status
- `/memory` - View memory statistics
- `/config` - Show current configuration
- `/clear` - Clear conversation history
- `/exit` - Exit JARVIS

### Example Conversations
```
JARVIS> hello
🤖 Hello! I'm JARVIS, your AI assistant. How can I help you today?

JARVIS> what time is it?
🤖 The current time is 14:30:25

JARVIS> /status
🤖 [Shows system status with AI provider info]
```

## 🛠️ Troubleshooting

### Common Issues

**"No module named 'yaml'"**
```bash
pip install PyYAML
```

**"API key not found"**
- JARVIS will run in test mode without API keys
- Add your API key to config.yaml or .env file for full functionality

**"Permission denied"**
```bash
# Make sure you're in the virtual environment
# Windows: .\venv\Scripts\activate
# macOS/Linux: source venv/bin/activate
```

### Getting Help
1. Check the logs in `logs/jarvis.log`
2. Use `/help` command in JARVIS
3. Read the full README.md
4. Check the project issues on GitHub

## 🎨 Customization

### Change AI Provider
Edit `jarvis/config/config.yaml`:
```yaml
ai:
  provider: "anthropic"  # or "openai"
  anthropic:
    api_key: "your-anthropic-key"
```

### Adjust Logging
```yaml
logging:
  level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
  file_path: "logs/jarvis.log"
```

### Enable Features
```yaml
system:
  file_operations:
    enabled: true
    safe_mode: true  # Requires confirmation for destructive operations
```

## 🚀 Next Steps

1. **Explore Features**: Try different commands and conversations
2. **Add Voice**: Configure speech recognition and TTS
3. **System Integration**: Enable file operations and app control
4. **Web Capabilities**: Add search and browsing features
5. **Personal Assistant**: Set up calendar and email integration

## 📚 Learn More

- **Full Documentation**: See README.md
- **Configuration Guide**: Check config.example.yaml
- **Development**: Explore the source code structure
- **Contributing**: Help improve JARVIS!

---

**Happy chatting with JARVIS! 🤖✨**
