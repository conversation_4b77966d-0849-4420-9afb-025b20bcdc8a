"""
System Monitor for JARVIS AI Assistant
"""

import psutil
import platform
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class SystemSnapshot:
    """System state snapshot"""
    timestamp: datetime = field(default_factory=datetime.now)
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    disk_usage_percent: float = 0.0
    network_bytes_sent: int = 0
    network_bytes_recv: int = 0
    process_count: int = 0
    boot_time: datetime = field(default_factory=datetime.now)


class SystemMonitor:
    """Monitors system resources and performance"""
    
    def __init__(self, history_size: int = 100):
        """Initialize system monitor"""
        self.history_size = history_size
        self.snapshots: List[SystemSnapshot] = []
        self.last_network_stats = None
        
        # Get initial network stats
        try:
            self.last_network_stats = psutil.net_io_counters()
        except Exception:
            pass
        
        logger.info("System monitor initialized")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        try:
            # Basic system info
            uname = platform.uname()
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            
            info = {
                "system": {
                    "platform": uname.system,
                    "node_name": uname.node,
                    "release": uname.release,
                    "version": uname.version,
                    "machine": uname.machine,
                    "processor": uname.processor,
                    "boot_time": boot_time.isoformat(),
                    "uptime_seconds": (datetime.now() - boot_time).total_seconds()
                },
                "python": {
                    "version": platform.python_version(),
                    "implementation": platform.python_implementation(),
                    "compiler": platform.python_compiler()
                }
            }
            
            # CPU information
            cpu_info = {
                "physical_cores": psutil.cpu_count(logical=False),
                "logical_cores": psutil.cpu_count(logical=True),
                "current_frequency": None,
                "min_frequency": None,
                "max_frequency": None,
                "usage_percent": psutil.cpu_percent(interval=1)
            }
            
            try:
                cpu_freq = psutil.cpu_freq()
                if cpu_freq:
                    cpu_info.update({
                        "current_frequency": cpu_freq.current,
                        "min_frequency": cpu_freq.min,
                        "max_frequency": cpu_freq.max
                    })
            except Exception:
                pass
            
            info["cpu"] = cpu_info
            
            # Memory information
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            info["memory"] = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percentage": memory.percent,
                "swap_total_gb": round(swap.total / (1024**3), 2),
                "swap_used_gb": round(swap.used / (1024**3), 2),
                "swap_percentage": swap.percent
            }
            
            # Disk information
            disk_info = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append({
                        "device": partition.device,
                        "mountpoint": partition.mountpoint,
                        "filesystem": partition.fstype,
                        "total_gb": round(usage.total / (1024**3), 2),
                        "used_gb": round(usage.used / (1024**3), 2),
                        "free_gb": round(usage.free / (1024**3), 2),
                        "percentage": round((usage.used / usage.total) * 100, 1)
                    })
                except PermissionError:
                    continue
            
            info["disks"] = disk_info
            
            # Network information
            try:
                network_stats = psutil.net_io_counters()
                info["network"] = {
                    "bytes_sent": network_stats.bytes_sent,
                    "bytes_received": network_stats.bytes_recv,
                    "packets_sent": network_stats.packets_sent,
                    "packets_received": network_stats.packets_recv,
                    "errors_in": network_stats.errin,
                    "errors_out": network_stats.errout,
                    "drops_in": network_stats.dropin,
                    "drops_out": network_stats.dropout
                }
            except Exception:
                info["network"] = {"error": "Network stats not available"}
            
            return {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "data": info
            }
            
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_current_usage(self) -> Dict[str, Any]:
        """Get current system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage (primary disk)
            disk_usage = psutil.disk_usage('/')
            if platform.system() == "Windows":
                disk_usage = psutil.disk_usage('C:')
            
            # Network usage
            network_stats = psutil.net_io_counters()
            network_speed = {"sent_mbps": 0, "recv_mbps": 0}
            
            if self.last_network_stats:
                time_diff = 1.0  # Approximate time difference
                bytes_sent_diff = network_stats.bytes_sent - self.last_network_stats.bytes_sent
                bytes_recv_diff = network_stats.bytes_recv - self.last_network_stats.bytes_recv
                
                network_speed = {
                    "sent_mbps": round((bytes_sent_diff / time_diff) / (1024**2), 2),
                    "recv_mbps": round((bytes_recv_diff / time_diff) / (1024**2), 2)
                }
            
            self.last_network_stats = network_stats
            
            # Process count
            process_count = len(list(psutil.process_iter()))
            
            usage_data = {
                "cpu": {
                    "overall_percent": cpu_percent,
                    "per_core": cpu_per_core,
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                "memory": {
                    "percent": memory.percent,
                    "used_gb": round(memory.used / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "total_gb": round(memory.total / (1024**3), 2)
                },
                "disk": {
                    "percent": round((disk_usage.used / disk_usage.total) * 100, 1),
                    "used_gb": round(disk_usage.used / (1024**3), 2),
                    "free_gb": round(disk_usage.free / (1024**3), 2),
                    "total_gb": round(disk_usage.total / (1024**3), 2)
                },
                "network": {
                    "speed": network_speed,
                    "total_sent_gb": round(network_stats.bytes_sent / (1024**3), 2),
                    "total_recv_gb": round(network_stats.bytes_recv / (1024**3), 2)
                },
                "processes": {
                    "count": process_count
                }
            }
            
            # Create snapshot for history
            snapshot = SystemSnapshot(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_usage_percent=usage_data["disk"]["percent"],
                network_bytes_sent=network_stats.bytes_sent,
                network_bytes_recv=network_stats.bytes_recv,
                process_count=process_count,
                boot_time=datetime.fromtimestamp(psutil.boot_time())
            )
            
            self._add_snapshot(snapshot)
            
            return {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "data": usage_data
            }
            
        except Exception as e:
            logger.error(f"Error getting current usage: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_top_processes(self, limit: int = 10, sort_by: str = "memory") -> Dict[str, Any]:
        """Get top processes by resource usage"""
        try:
            processes = []
            
            for process in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'memory_info', 'status']):
                try:
                    process_info = process.info
                    processes.append({
                        "pid": process_info['pid'],
                        "name": process_info['name'],
                        "cpu_percent": process_info['cpu_percent'],
                        "memory_percent": round(process_info['memory_percent'], 2),
                        "memory_mb": round(process_info['memory_info'].rss / (1024**2), 1),
                        "status": process_info['status']
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort processes
            if sort_by == "cpu":
                processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            elif sort_by == "memory":
                processes.sort(key=lambda x: x['memory_percent'], reverse=True)
            else:
                processes.sort(key=lambda x: x['memory_percent'], reverse=True)
            
            return {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "sort_by": sort_by,
                "processes": processes[:limit],
                "total_processes": len(processes)
            }
            
        except Exception as e:
            logger.error(f"Error getting top processes: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_usage_history(self, hours: int = 1) -> Dict[str, Any]:
        """Get usage history for specified time period"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_snapshots = [
                s for s in self.snapshots 
                if s.timestamp >= cutoff_time
            ]
            
            if not recent_snapshots:
                return {
                    "success": False,
                    "error": "No usage history available for the specified period"
                }
            
            history_data = {
                "timestamps": [s.timestamp.isoformat() for s in recent_snapshots],
                "cpu_percent": [s.cpu_percent for s in recent_snapshots],
                "memory_percent": [s.memory_percent for s in recent_snapshots],
                "disk_percent": [s.disk_usage_percent for s in recent_snapshots],
                "process_count": [s.process_count for s in recent_snapshots]
            }
            
            # Calculate averages
            averages = {
                "cpu_avg": round(sum(history_data["cpu_percent"]) / len(history_data["cpu_percent"]), 2),
                "memory_avg": round(sum(history_data["memory_percent"]) / len(history_data["memory_percent"]), 2),
                "disk_avg": round(sum(history_data["disk_percent"]) / len(history_data["disk_percent"]), 2),
                "process_avg": round(sum(history_data["process_count"]) / len(history_data["process_count"]), 1)
            }
            
            return {
                "success": True,
                "period_hours": hours,
                "data_points": len(recent_snapshots),
                "history": history_data,
                "averages": averages
            }
            
        except Exception as e:
            logger.error(f"Error getting usage history: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def check_system_health(self) -> Dict[str, Any]:
        """Check overall system health and identify issues"""
        try:
            current_usage = self.get_current_usage()
            if not current_usage["success"]:
                return current_usage
            
            data = current_usage["data"]
            issues = []
            warnings = []
            
            # Check CPU usage
            if data["cpu"]["overall_percent"] > 90:
                issues.append("High CPU usage detected")
            elif data["cpu"]["overall_percent"] > 75:
                warnings.append("Elevated CPU usage")
            
            # Check memory usage
            if data["memory"]["percent"] > 90:
                issues.append("High memory usage detected")
            elif data["memory"]["percent"] > 80:
                warnings.append("Elevated memory usage")
            
            # Check disk usage
            if data["disk"]["percent"] > 95:
                issues.append("Disk space critically low")
            elif data["disk"]["percent"] > 85:
                warnings.append("Disk space running low")
            
            # Check available memory
            if data["memory"]["available_gb"] < 1:
                issues.append("Very low available memory")
            elif data["memory"]["available_gb"] < 2:
                warnings.append("Low available memory")
            
            # Determine overall health status
            if issues:
                health_status = "critical"
            elif warnings:
                health_status = "warning"
            else:
                health_status = "good"
            
            return {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "health_status": health_status,
                "issues": issues,
                "warnings": warnings,
                "current_usage": data,
                "recommendations": self._get_health_recommendations(issues, warnings, data)
            }
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _add_snapshot(self, snapshot: SystemSnapshot):
        """Add snapshot to history"""
        self.snapshots.append(snapshot)
        
        # Keep only recent snapshots
        if len(self.snapshots) > self.history_size:
            self.snapshots = self.snapshots[-self.history_size:]
    
    def _get_health_recommendations(self, issues: List[str], warnings: List[str], data: Dict[str, Any]) -> List[str]:
        """Get health recommendations based on current state"""
        recommendations = []
        
        if data["cpu"]["overall_percent"] > 75:
            recommendations.append("Consider closing unnecessary applications to reduce CPU load")
        
        if data["memory"]["percent"] > 80:
            recommendations.append("Close memory-intensive applications or restart the system")
        
        if data["disk"]["percent"] > 85:
            recommendations.append("Free up disk space by deleting unnecessary files")
        
        if data["processes"]["count"] > 200:
            recommendations.append("Consider reducing the number of running processes")
        
        if not recommendations:
            recommendations.append("System is running optimally")
        
        return recommendations
    
    def get_stats(self) -> Dict[str, Any]:
        """Get system monitor statistics"""
        return {
            "snapshots_collected": len(self.snapshots),
            "history_size_limit": self.history_size,
            "monitoring_duration_hours": (
                (self.snapshots[-1].timestamp - self.snapshots[0].timestamp).total_seconds() / 3600
                if len(self.snapshots) > 1 else 0
            )
        }
