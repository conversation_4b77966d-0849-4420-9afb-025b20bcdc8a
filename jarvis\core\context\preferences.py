"""
User Preferences Management for JARVIS AI Assistant
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, field, asdict

from ...config import config

logger = logging.getLogger(__name__)


@dataclass
class PreferenceSchema:
    """Schema for a user preference"""
    key: str
    value_type: str  # str, int, float, bool, list, dict
    default_value: Any
    description: str
    category: str
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    user_editable: bool = True


@dataclass
class UserProfile:
    """Complete user profile with preferences"""
    user_id: str
    username: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    preferences: Dict[str, Any] = field(default_factory=dict)
    learned_preferences: Dict[str, Any] = field(default_factory=dict)
    behavior_profile: Dict[str, Any] = field(default_factory=dict)
    customizations: Dict[str, Any] = field(default_factory=dict)


class UserPreferences:
    """Advanced user preferences management system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize user preferences system"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.preferences_file = self.data_dir / "user_preferences.json"
        self.profiles_file = self.data_dir / "user_profiles.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # User profiles and preferences
        self.user_profiles: Dict[str, UserProfile] = {}
        self.preference_schemas: Dict[str, PreferenceSchema] = {}
        
        # Initialize default preference schemas
        self._initialize_preference_schemas()
        
        # Load existing data
        self._load_user_profiles()
        
        logger.info("User preferences system initialized")
    
    def _initialize_preference_schemas(self):
        """Initialize default preference schemas"""
        schemas = [
            # Interface preferences
            PreferenceSchema(
                key="interface.theme",
                value_type="str",
                default_value="dark",
                description="UI theme preference",
                category="interface",
                validation_rules={"choices": ["dark", "light", "auto"]}
            ),
            PreferenceSchema(
                key="interface.language",
                value_type="str",
                default_value="en",
                description="Interface language",
                category="interface",
                validation_rules={"choices": ["en", "es", "fr", "de", "zh"]}
            ),
            PreferenceSchema(
                key="interface.notifications",
                value_type="bool",
                default_value=True,
                description="Enable desktop notifications",
                category="interface"
            ),
            
            # Communication preferences
            PreferenceSchema(
                key="communication.style",
                value_type="str",
                default_value="professional",
                description="Communication style preference",
                category="communication",
                validation_rules={"choices": ["professional", "friendly", "casual", "technical"]}
            ),
            PreferenceSchema(
                key="communication.verbosity",
                value_type="str",
                default_value="balanced",
                description="Response verbosity level",
                category="communication",
                validation_rules={"choices": ["concise", "balanced", "detailed", "verbose"]}
            ),
            PreferenceSchema(
                key="communication.formality",
                value_type="str",
                default_value="polite",
                description="Formality level in responses",
                category="communication",
                validation_rules={"choices": ["casual", "polite", "formal", "professional"]}
            ),
            
            # Voice preferences
            PreferenceSchema(
                key="voice.enabled",
                value_type="bool",
                default_value=True,
                description="Enable voice interface",
                category="voice"
            ),
            PreferenceSchema(
                key="voice.provider",
                value_type="str",
                default_value="pyttsx3",
                description="Text-to-speech provider",
                category="voice",
                validation_rules={"choices": ["pyttsx3", "gtts", "elevenlabs", "azure"]}
            ),
            PreferenceSchema(
                key="voice.speed",
                value_type="float",
                default_value=1.0,
                description="Voice speaking speed",
                category="voice",
                validation_rules={"min": 0.5, "max": 2.0}
            ),
            PreferenceSchema(
                key="voice.wake_word",
                value_type="str",
                default_value="jarvis",
                description="Voice activation wake word",
                category="voice",
                validation_rules={"choices": ["jarvis", "computer", "assistant", "hey jarvis"]}
            ),
            
            # Task preferences
            PreferenceSchema(
                key="tasks.auto_confirm",
                value_type="bool",
                default_value=False,
                description="Auto-confirm safe operations",
                category="tasks"
            ),
            PreferenceSchema(
                key="tasks.backup_files",
                value_type="bool",
                default_value=True,
                description="Create backups before file operations",
                category="tasks"
            ),
            PreferenceSchema(
                key="tasks.max_concurrent",
                value_type="int",
                default_value=5,
                description="Maximum concurrent tasks",
                category="tasks",
                validation_rules={"min": 1, "max": 20}
            ),
            
            # Privacy preferences
            PreferenceSchema(
                key="privacy.data_collection",
                value_type="bool",
                default_value=True,
                description="Allow data collection for improvements",
                category="privacy"
            ),
            PreferenceSchema(
                key="privacy.conversation_logging",
                value_type="bool",
                default_value=True,
                description="Log conversations for context",
                category="privacy"
            ),
            PreferenceSchema(
                key="privacy.analytics",
                value_type="bool",
                default_value=False,
                description="Share anonymous usage analytics",
                category="privacy"
            ),
            
            # Automation preferences
            PreferenceSchema(
                key="automation.proactive_suggestions",
                value_type="bool",
                default_value=True,
                description="Enable proactive suggestions",
                category="automation"
            ),
            PreferenceSchema(
                key="automation.smart_scheduling",
                value_type="bool",
                default_value=True,
                description="Enable smart task scheduling",
                category="automation"
            ),
            PreferenceSchema(
                key="automation.learning_mode",
                value_type="bool",
                default_value=True,
                description="Learn from user behavior",
                category="automation"
            )
        ]
        
        for schema in schemas:
            self.preference_schemas[schema.key] = schema
    
    def get_user_profile(self, user_id: str) -> UserProfile:
        """Get or create user profile"""
        if user_id not in self.user_profiles:
            profile = UserProfile(user_id=user_id)
            
            # Initialize with default preferences
            for key, schema in self.preference_schemas.items():
                profile.preferences[key] = schema.default_value
            
            self.user_profiles[user_id] = profile
            self._save_user_profiles()
            
            logger.info(f"Created new user profile: {user_id}")
        
        return self.user_profiles[user_id]
    
    def set_preference(
        self, 
        user_id: str, 
        key: str, 
        value: Any, 
        learned: bool = False
    ) -> bool:
        """Set a user preference"""
        try:
            # Validate preference key
            if key not in self.preference_schemas:
                logger.warning(f"Unknown preference key: {key}")
                return False
            
            schema = self.preference_schemas[key]
            
            # Check if user can edit this preference
            if not learned and not schema.user_editable:
                logger.warning(f"Preference {key} is not user-editable")
                return False
            
            # Validate value
            if not self._validate_preference_value(schema, value):
                logger.warning(f"Invalid value for preference {key}: {value}")
                return False
            
            # Get user profile
            profile = self.get_user_profile(user_id)
            
            # Set preference
            if learned:
                profile.learned_preferences[key] = value
            else:
                profile.preferences[key] = value
            
            profile.last_updated = datetime.now()
            
            # Save changes
            self._save_user_profiles()
            
            logger.info(f"Set preference {key} = {value} for user {user_id} (learned: {learned})")
            return True
            
        except Exception as e:
            logger.error(f"Error setting preference: {e}")
            return False
    
    def get_preference(
        self, 
        user_id: str, 
        key: str, 
        default: Any = None,
        include_learned: bool = True
    ) -> Any:
        """Get a user preference"""
        try:
            profile = self.get_user_profile(user_id)
            
            # Check learned preferences first if enabled
            if include_learned and key in profile.learned_preferences:
                return profile.learned_preferences[key]
            
            # Check explicit preferences
            if key in profile.preferences:
                return profile.preferences[key]
            
            # Check schema default
            if key in self.preference_schemas:
                return self.preference_schemas[key].default_value
            
            # Return provided default
            return default
            
        except Exception as e:
            logger.error(f"Error getting preference: {e}")
            return default
    
    def get_preferences_by_category(self, user_id: str, category: str) -> Dict[str, Any]:
        """Get all preferences in a category"""
        profile = self.get_user_profile(user_id)
        category_prefs = {}
        
        for key, schema in self.preference_schemas.items():
            if schema.category == category:
                value = self.get_preference(user_id, key)
                category_prefs[key] = value
        
        return category_prefs
    
    def update_behavior_profile(self, user_id: str, behavior_data: Dict[str, Any]):
        """Update user behavior profile"""
        profile = self.get_user_profile(user_id)
        
        # Merge behavior data
        for key, value in behavior_data.items():
            profile.behavior_profile[key] = value
        
        profile.last_updated = datetime.now()
        self._save_user_profiles()
    
    def learn_preference_from_behavior(self, user_id: str, behavior_pattern: str, confidence: float = 0.8):
        """Learn user preferences from behavior patterns"""
        if confidence < 0.6:  # Only learn from high-confidence patterns
            return
        
        # Pattern-based preference learning
        learning_rules = {
            "prefers_detailed_responses": ("communication.verbosity", "detailed"),
            "prefers_concise_responses": ("communication.verbosity", "concise"),
            "uses_formal_language": ("communication.formality", "formal"),
            "uses_casual_language": ("communication.formality", "casual"),
            "frequent_voice_user": ("voice.enabled", True),
            "avoids_voice_interface": ("voice.enabled", False),
            "prefers_auto_confirmation": ("tasks.auto_confirm", True),
            "prefers_manual_confirmation": ("tasks.auto_confirm", False)
        }
        
        if behavior_pattern in learning_rules:
            pref_key, pref_value = learning_rules[behavior_pattern]
            self.set_preference(user_id, pref_key, pref_value, learned=True)
            
            logger.info(f"Learned preference from behavior: {pref_key} = {pref_value}")
    
    def get_personalization_settings(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive personalization settings"""
        profile = self.get_user_profile(user_id)
        
        return {
            "user_profile": {
                "user_id": profile.user_id,
                "username": profile.username,
                "created_at": profile.created_at.isoformat(),
                "last_updated": profile.last_updated.isoformat()
            },
            "preferences": profile.preferences,
            "learned_preferences": profile.learned_preferences,
            "behavior_profile": profile.behavior_profile,
            "customizations": profile.customizations,
            "preference_categories": self._get_preference_categories()
        }
    
    def export_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Export user preferences for backup or transfer"""
        profile = self.get_user_profile(user_id)
        
        return {
            "user_id": profile.user_id,
            "username": profile.username,
            "export_date": datetime.now().isoformat(),
            "preferences": profile.preferences,
            "learned_preferences": profile.learned_preferences,
            "behavior_profile": profile.behavior_profile,
            "customizations": profile.customizations
        }
    
    def import_user_preferences(self, user_id: str, preferences_data: Dict[str, Any]) -> bool:
        """Import user preferences from backup"""
        try:
            profile = self.get_user_profile(user_id)
            
            # Import preferences with validation
            if "preferences" in preferences_data:
                for key, value in preferences_data["preferences"].items():
                    if key in self.preference_schemas:
                        if self._validate_preference_value(self.preference_schemas[key], value):
                            profile.preferences[key] = value
            
            # Import learned preferences
            if "learned_preferences" in preferences_data:
                profile.learned_preferences.update(preferences_data["learned_preferences"])
            
            # Import behavior profile
            if "behavior_profile" in preferences_data:
                profile.behavior_profile.update(preferences_data["behavior_profile"])
            
            # Import customizations
            if "customizations" in preferences_data:
                profile.customizations.update(preferences_data["customizations"])
            
            profile.last_updated = datetime.now()
            self._save_user_profiles()
            
            logger.info(f"Imported preferences for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error importing preferences: {e}")
            return False
    
    def _validate_preference_value(self, schema: PreferenceSchema, value: Any) -> bool:
        """Validate a preference value against its schema"""
        try:
            # Type validation
            expected_type = {
                "str": str,
                "int": int,
                "float": (int, float),
                "bool": bool,
                "list": list,
                "dict": dict
            }.get(schema.value_type)
            
            if expected_type and not isinstance(value, expected_type):
                return False
            
            # Validation rules
            rules = schema.validation_rules
            
            if "choices" in rules and value not in rules["choices"]:
                return False
            
            if "min" in rules and value < rules["min"]:
                return False
            
            if "max" in rules and value > rules["max"]:
                return False
            
            if "pattern" in rules:
                import re
                if not re.match(rules["pattern"], str(value)):
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _get_preference_categories(self) -> Dict[str, List[str]]:
        """Get preferences organized by category"""
        categories = {}
        
        for key, schema in self.preference_schemas.items():
            if schema.category not in categories:
                categories[schema.category] = []
            categories[schema.category].append(key)
        
        return categories
    
    def _load_user_profiles(self):
        """Load user profiles from file"""
        try:
            if self.profiles_file.exists():
                with open(self.profiles_file, 'r') as f:
                    profiles_data = json.load(f)
                
                for user_id, profile_data in profiles_data.items():
                    # Convert datetime strings back to datetime objects
                    if "created_at" in profile_data:
                        profile_data["created_at"] = datetime.fromisoformat(profile_data["created_at"])
                    if "last_updated" in profile_data:
                        profile_data["last_updated"] = datetime.fromisoformat(profile_data["last_updated"])
                    
                    profile = UserProfile(**profile_data)
                    self.user_profiles[user_id] = profile
                
                logger.info(f"Loaded {len(self.user_profiles)} user profiles")
                
        except Exception as e:
            logger.error(f"Error loading user profiles: {e}")
    
    def _save_user_profiles(self):
        """Save user profiles to file"""
        try:
            profiles_data = {}
            
            for user_id, profile in self.user_profiles.items():
                profile_dict = asdict(profile)
                # Convert datetime objects to strings
                profile_dict["created_at"] = profile.created_at.isoformat()
                profile_dict["last_updated"] = profile.last_updated.isoformat()
                profiles_data[user_id] = profile_dict
            
            with open(self.profiles_file, 'w') as f:
                json.dump(profiles_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving user profiles: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get preferences system statistics"""
        return {
            "total_users": len(self.user_profiles),
            "total_preference_schemas": len(self.preference_schemas),
            "preference_categories": list(self._get_preference_categories().keys()),
            "users_with_learned_preferences": sum(
                1 for profile in self.user_profiles.values() 
                if profile.learned_preferences
            )
        }
