"""
Electron Desktop Application for JARVIS AI Assistant
"""

import os
import json
import logging
import asyncio
import subprocess
from typing import Dict, Any, Optional
from pathlib import Path
import threading
import time

from ...config import config

logger = logging.getLogger(__name__)


class ElectronApp:
    """Electron-based desktop application for JARVIS"""
    
    def __init__(self, jarvis_core=None):
        """Initialize Electron app"""
        self.jarvis_core = jarvis_core
        self.app_path = Path(__file__).parent / "app"
        self.is_running = False
        self.process = None
        self.port = config.get("gui.port", 3000)
        
        # Create app directory structure
        self._create_app_structure()
        
        logger.info("Electron app initialized")
    
    def _create_app_structure(self):
        """Create Electron app directory structure"""
        try:
            # Create main directories
            directories = [
                self.app_path,
                self.app_path / "src",
                self.app_path / "src" / "components",
                self.app_path / "src" / "pages",
                self.app_path / "src" / "utils",
                self.app_path / "public",
                self.app_path / "build"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Create package.json if it doesn't exist
            package_json_path = self.app_path / "package.json"
            if not package_json_path.exists():
                self._create_package_json()
            
            # Create main Electron file
            main_js_path = self.app_path / "main.js"
            if not main_js_path.exists():
                self._create_main_js()
            
            # Create React app files
            self._create_react_files()
            
            logger.info("Electron app structure created")
            
        except Exception as e:
            logger.error(f"Error creating app structure: {e}")
    
    def _create_package_json(self):
        """Create package.json for Electron app"""
        package_json = {
            "name": "jarvis-desktop",
            "version": "1.0.0",
            "description": "JARVIS AI Assistant Desktop Application",
            "main": "main.js",
            "scripts": {
                "start": "electron .",
                "dev": "concurrently \"npm run react-dev\" \"wait-on http://localhost:3000 && electron .\"",
                "react-dev": "react-scripts start",
                "build": "react-scripts build",
                "electron-build": "electron-builder",
                "dist": "npm run build && electron-builder"
            },
            "dependencies": {
                "electron": "^22.0.0",
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-scripts": "5.0.1",
                "@mui/material": "^5.11.0",
                "@mui/icons-material": "^5.11.0",
                "@emotion/react": "^11.10.0",
                "@emotion/styled": "^11.10.0",
                "socket.io-client": "^4.6.0",
                "recharts": "^2.5.0",
                "axios": "^1.3.0"
            },
            "devDependencies": {
                "concurrently": "^7.6.0",
                "wait-on": "^7.0.0",
                "electron-builder": "^23.6.0"
            },
            "homepage": "./",
            "browserslist": {
                "production": [
                    ">0.2%",
                    "not dead",
                    "not op_mini all"
                ],
                "development": [
                    "last 1 chrome version",
                    "last 1 firefox version",
                    "last 1 safari version"
                ]
            }
        }
        
        with open(self.app_path / "package.json", 'w') as f:
            json.dump(package_json, f, indent=2)
    
    def _create_main_js(self):
        """Create main Electron process file"""
        main_js_content = '''const { app, BrowserWindow, ipcMain, Tray, Menu, nativeImage } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');

let mainWindow;
let tray;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    titleBarStyle: 'hiddenInset',
    show: false
  });

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle minimize to tray
  mainWindow.on('minimize', (event) => {
    if (process.platform === 'darwin') {
      event.preventDefault();
      mainWindow.hide();
    }
  });
}

function createTray() {
  const iconPath = path.join(__dirname, 'assets/tray-icon.png');
  const trayIcon = nativeImage.createFromPath(iconPath);
  tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show JARVIS',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        }
      }
    },
    {
      label: 'Voice Mode',
      type: 'checkbox',
      checked: false,
      click: (item) => {
        // Toggle voice mode
        if (mainWindow) {
          mainWindow.webContents.send('toggle-voice-mode', item.checked);
        }
      }
    },
    { type: 'separator' },
    {
      label: 'Quit JARVIS',
      click: () => {
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
  tray.setToolTip('JARVIS AI Assistant');

  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    }
  });
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createTray();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('minimize-to-tray', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});

ipcMain.handle('send-message', async (event, message) => {
  // Forward message to Python backend
  try {
    // This would connect to the Python JARVIS backend
    return { success: true, response: "Message received" };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
'''
        
        with open(self.app_path / "main.js", 'w') as f:
            f.write(main_js_content)
    
    def _create_react_files(self):
        """Create React application files"""
        # Create public/index.html
        index_html = '''<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="JARVIS AI Assistant" />
    <title>JARVIS AI Assistant</title>
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>'''
        
        with open(self.app_path / "public" / "index.html", 'w') as f:
            f.write(index_html)
        
        # Create src/index.js
        index_js = '''import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);'''
        
        with open(self.app_path / "src" / "index.js", 'w') as f:
            f.write(index_js)
        
        # Create src/App.js
        app_js = '''import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, Container } from '@mui/material';
import ChatInterface from './components/ChatInterface';
import Dashboard from './components/Dashboard';
import VoiceControls from './components/VoiceControls';
import StatusBar from './components/StatusBar';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00bcd4',
    },
    secondary: {
      main: '#ff4081',
    },
    background: {
      default: '#0a0a0a',
      paper: '#1a1a1a',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
  },
});

function App() {
  const [currentView, setCurrentView] = useState('chat');
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    cpu: 0,
    memory: 0,
    network: 'connected'
  });

  useEffect(() => {
    // Listen for voice mode toggle from main process
    if (window.electronAPI) {
      window.electronAPI.onToggleVoiceMode((enabled) => {
        setIsVoiceMode(enabled);
      });
    }

    // Simulate system status updates
    const interval = setInterval(() => {
      setSystemStatus({
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        network: Math.random() > 0.1 ? 'connected' : 'disconnected'
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)'
      }}>
        <StatusBar
          status={systemStatus}
          isVoiceMode={isVoiceMode}
          onViewChange={setCurrentView}
          currentView={currentView}
        />

        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          {currentView === 'chat' && (
            <ChatInterface isVoiceMode={isVoiceMode} />
          )}
          {currentView === 'dashboard' && (
            <Dashboard systemStatus={systemStatus} />
          )}
        </Box>

        {isVoiceMode && (
          <VoiceControls onToggleVoice={setIsVoiceMode} />
        )}
      </Box>
    </ThemeProvider>
  );
}

export default App;'''

        with open(self.app_path / "src" / "App.js", 'w') as f:
            f.write(app_js)

        # Create React components
        self._create_react_components()

    def _create_react_components(self):
        """Create React component files"""
        components_dir = self.app_path / "src" / "components"

        # Create ChatInterface component
        chat_interface = '''import React, { useState, useEffect, useRef } from 'react';
import {
  Box, Paper, TextField, IconButton, Typography, Avatar,
  List, ListItem, ListItemText, ListItemAvatar, Chip
} from '@mui/material';
import { Send, Mic, MicOff, VolumeUp } from '@mui/icons-material';

function ChatInterface({ isVoiceMode }) {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm JARVIS, your AI assistant. How can I help you today?",
      sender: 'jarvis',
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate JARVIS response
    setTimeout(() => {
      const jarvisResponse = {
        id: Date.now() + 1,
        text: `I understand you said: "${inputText}". Let me help you with that.`,
        sender: 'jarvis',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, jarvisResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const toggleListening = () => {
    setIsListening(!isListening);
    // Voice recognition logic would go here
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Messages Area */}
      <Box sx={{
        flex: 1,
        overflow: 'auto',
        p: 2,
        background: 'rgba(0,0,0,0.3)'
      }}>
        <List>
          {messages.map((message) => (
            <ListItem
              key={message.id}
              sx={{
                justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                mb: 1
              }}
            >
              <Paper
                sx={{
                  p: 2,
                  maxWidth: '70%',
                  backgroundColor: message.sender === 'user'
                    ? 'primary.main'
                    : 'background.paper',
                  color: message.sender === 'user' ? 'white' : 'text.primary'
                }}
              >
                <Typography variant="body1">{message.text}</Typography>
                <Typography variant="caption" sx={{ opacity: 0.7, mt: 1, display: 'block' }}>
                  {message.timestamp.toLocaleTimeString()}
                </Typography>
              </Paper>
            </ListItem>
          ))}
          {isTyping && (
            <ListItem>
              <Paper sx={{ p: 2, backgroundColor: 'background.paper' }}>
                <Typography variant="body1">JARVIS is thinking...</Typography>
              </Paper>
            </ListItem>
          )}
        </List>
        <div ref={messagesEndRef} />
      </Box>

      {/* Input Area */}
      <Box sx={{
        p: 2,
        backgroundColor: 'background.paper',
        borderTop: 1,
        borderColor: 'divider'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message to JARVIS..."
            variant="outlined"
            size="small"
          />
          <IconButton
            color="primary"
            onClick={toggleListening}
            sx={{
              backgroundColor: isListening ? 'error.main' : 'transparent',
              '&:hover': {
                backgroundColor: isListening ? 'error.dark' : 'action.hover'
              }
            }}
          >
            {isListening ? <MicOff /> : <Mic />}
          </IconButton>
          <IconButton
            color="primary"
            onClick={handleSendMessage}
            disabled={!inputText.trim()}
          >
            <Send />
          </IconButton>
        </Box>
        {isVoiceMode && (
          <Box sx={{ mt: 1, display: 'flex', justifyContent: 'center' }}>
            <Chip
              icon={<VolumeUp />}
              label="Voice Mode Active"
              color="secondary"
              variant="outlined"
            />
          </Box>
        )}
      </Box>
    </Box>
  );
}

export default ChatInterface;'''

        with open(components_dir / "ChatInterface.js", 'w') as f:
            f.write(chat_interface)

    async def start(self):
        """Start the Electron application"""
        try:
            if self.is_running:
                logger.warning("Electron app is already running")
                return {"success": False, "error": "App already running"}
            
            # Check if Node.js and npm are available
            if not self._check_dependencies():
                return {
                    "success": False,
                    "error": "Node.js and npm are required for the GUI"
                }
            
            # Install dependencies if needed
            await self._install_dependencies()
            
            # Start the Electron app
            await self._start_electron()
            
            self.is_running = True
            logger.info("Electron app started successfully")
            
            return {
                "success": True,
                "message": "GUI application started",
                "port": self.port
            }
            
        except Exception as e:
            logger.error(f"Error starting Electron app: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _check_dependencies(self) -> bool:
        """Check if Node.js and npm are available"""
        try:
            subprocess.run(["node", "--version"], 
                         capture_output=True, check=True)
            subprocess.run(["npm", "--version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Node.js and npm are required but not found")
            return False
    
    async def _install_dependencies(self):
        """Install npm dependencies"""
        try:
            package_json_path = self.app_path / "package.json"
            node_modules_path = self.app_path / "node_modules"
            
            if not node_modules_path.exists() or not any(node_modules_path.iterdir()):
                logger.info("Installing npm dependencies...")
                
                process = await asyncio.create_subprocess_exec(
                    "npm", "install",
                    cwd=str(self.app_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode != 0:
                    raise Exception(f"npm install failed: {stderr.decode()}")
                
                logger.info("Dependencies installed successfully")
            
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            raise
    
    async def _start_electron(self):
        """Start the Electron process"""
        try:
            # Start in development mode
            self.process = await asyncio.create_subprocess_exec(
                "npm", "run", "dev",
                cwd=str(self.app_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Give it a moment to start
            await asyncio.sleep(3)
            
            if self.process.returncode is not None:
                stdout, stderr = await self.process.communicate()
                raise Exception(f"Electron failed to start: {stderr.decode()}")
            
        except Exception as e:
            logger.error(f"Error starting Electron: {e}")
            raise
    
    async def stop(self):
        """Stop the Electron application"""
        try:
            if self.process and self.process.returncode is None:
                self.process.terminate()
                await self.process.wait()
            
            self.is_running = False
            logger.info("Electron app stopped")
            
            return {
                "success": True,
                "message": "GUI application stopped"
            }
            
        except Exception as e:
            logger.error(f"Error stopping Electron app: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get application status"""
        return {
            "running": self.is_running,
            "port": self.port,
            "app_path": str(self.app_path),
            "process_id": self.process.pid if self.process else None
        }
