"""
Helper utilities for JARVIS AI Assistant
"""

import re
from datetime import datetime, timed<PERSON><PERSON>
from typing import Union


def format_time(seconds: float) -> str:
    """
    Format time duration in a human-readable format
    
    Args:
        seconds: Time duration in seconds
        
    Returns:
        Formatted time string
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes:.0f}m {remaining_seconds:.0f}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours:.0f}h {remaining_minutes:.0f}m"


def format_size(bytes_size: Union[int, float]) -> str:
    """
    Format file size in a human-readable format
    
    Args:
        bytes_size: Size in bytes
        
    Returns:
        Formatted size string
    """
    if bytes_size < 1024:
        return f"{bytes_size:.0f} B"
    elif bytes_size < 1024 * 1024:
        return f"{bytes_size/1024:.1f} KB"
    elif bytes_size < 1024 * 1024 * 1024:
        return f"{bytes_size/(1024*1024):.1f} MB"
    else:
        return f"{bytes_size/(1024*1024*1024):.1f} GB"


def safe_filename(filename: str) -> str:
    """
    Convert a string to a safe filename
    
    Args:
        filename: Original filename
        
    Returns:
        Safe filename
    """
    # Remove or replace unsafe characters
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing spaces and dots
    safe_name = safe_name.strip(' .')
    
    # Limit length
    if len(safe_name) > 255:
        safe_name = safe_name[:255]
    
    # Ensure it's not empty
    if not safe_name:
        safe_name = "untitled"
    
    return safe_name


def parse_time_string(time_str: str) -> timedelta:
    """
    Parse a time string into a timedelta object
    
    Args:
        time_str: Time string (e.g., "5m", "2h", "30s")
        
    Returns:
        timedelta object
    """
    time_str = time_str.lower().strip()
    
    # Regular expression to match time patterns
    pattern = r'(\d+(?:\.\d+)?)\s*([smhd])'
    matches = re.findall(pattern, time_str)
    
    total_seconds = 0
    
    for value, unit in matches:
        value = float(value)
        
        if unit == 's':
            total_seconds += value
        elif unit == 'm':
            total_seconds += value * 60
        elif unit == 'h':
            total_seconds += value * 3600
        elif unit == 'd':
            total_seconds += value * 86400
    
    return timedelta(seconds=total_seconds)


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate text to a maximum length
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def is_valid_email(email: str) -> bool:
    """
    Check if an email address is valid
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid, False otherwise
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def extract_urls(text: str) -> list:
    """
    Extract URLs from text
    
    Args:
        text: Text to search for URLs
        
    Returns:
        List of URLs found
    """
    url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    return re.findall(url_pattern, text)
