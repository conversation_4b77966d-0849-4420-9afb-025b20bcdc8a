"""
Main Security Manager for JARVIS AI Assistant
"""

import logging
from typing import Dict, Optional, Tuple, Any, List
from datetime import datetime
import asyncio

from .authentication import Authenti<PERSON><PERSON><PERSON><PERSON>
from .authorization import AuthorizationManager
from .encryption import EncryptionManager
from .audit import AuditLogger, AuditEventType, AuditSeverity
from ..config import config

logger = logging.getLogger(__name__)


class SecurityManager:
    """Comprehensive security management system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize security manager"""
        self.data_dir = data_dir
        
        # Initialize security components
        self.auth_manager = AuthenticationManager(data_dir)
        self.authz_manager = AuthorizationManager(data_dir)
        self.encryption_manager = EncryptionManager(data_dir)
        self.audit_logger = AuditLogger(data_dir)
        
        # Security settings
        self.security_enabled = config.get("security.enabled", True)
        self.require_authentication = config.get("security.require_authentication", True)
        self.enforce_permissions = config.get("security.enforce_permissions", True)
        self.audit_all_actions = config.get("security.audit_all_actions", True)
        
        # Session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        logger.info("Security manager initialized")
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, Optional[str], str]:
        """Authenticate user and create session"""
        try:
            # Log authentication attempt
            self.audit_logger.log_authentication_event(
                action="login_attempt",
                details={"username": username},
                source_ip=source_ip
            )
            
            # Authenticate with auth manager
            success, user_id, message = self.auth_manager.authenticate_user(username, password)
            
            if success and user_id:
                # Create authentication token
                token_success, token, token_message = self.auth_manager.create_token(user_id)
                
                if token_success and token:
                    # Assign default role if user has no roles
                    user_roles = self.authz_manager.get_user_roles(user_id)
                    if not user_roles:
                        self.authz_manager.assign_role(user_id, "basic_user")
                    
                    # Log successful authentication
                    self.audit_logger.log_authentication_event(
                        action="login_success",
                        user_id=user_id,
                        result="success",
                        details={"username": username},
                        source_ip=source_ip
                    )
                    
                    # Track session
                    self.active_sessions[token] = {
                        "user_id": user_id,
                        "username": username,
                        "login_time": datetime.now(),
                        "source_ip": source_ip,
                        "user_agent": user_agent
                    }
                    
                    return True, token, "Authentication successful"
                else:
                    # Log token creation failure
                    self.audit_logger.log_authentication_event(
                        action="token_creation_failed",
                        user_id=user_id,
                        result="failure",
                        details={"username": username, "error": token_message},
                        source_ip=source_ip
                    )
                    return False, None, "Failed to create authentication token"
            else:
                # Log failed authentication
                self.audit_logger.log_authentication_event(
                    action="login_failed",
                    result="failure",
                    details={"username": username, "error": message},
                    source_ip=source_ip
                )
                return False, None, message
                
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            self.audit_logger.log_error_event(
                action="authentication_error",
                error_message=str(e),
                details={"username": username},
                severity=AuditSeverity.HIGH
            )
            return False, None, "Authentication error"
    
    async def validate_session(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """Validate user session token"""
        try:
            # Validate token with auth manager
            valid, payload, message = self.auth_manager.validate_token(token)
            
            if valid and payload:
                user_id = payload.get("user_id")
                
                # Update session tracking
                if token in self.active_sessions:
                    self.active_sessions[token]["last_activity"] = datetime.now()
                
                return True, payload, "Session valid"
            else:
                # Remove invalid session
                if token in self.active_sessions:
                    del self.active_sessions[token]
                
                return False, None, message
                
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return False, None, "Session validation error"
    
    async def check_permission(
        self,
        token: str,
        permission: str,
        resource: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, str]:
        """Check if user has permission for action"""
        try:
            if not self.enforce_permissions:
                return True, "Permission enforcement disabled"
            
            # Validate session first
            valid, payload, message = await self.validate_session(token)
            if not valid:
                return False, message
            
            user_id = payload.get("user_id")
            
            # Check permission with authorization manager
            has_permission = self.authz_manager.check_permission(
                user_id, permission, resource, context
            )
            
            # Log authorization event
            self.audit_logger.log_authorization_event(
                action=f"check_permission_{permission}",
                user_id=user_id,
                resource=resource,
                result="success" if has_permission else "failure",
                details={
                    "permission": permission,
                    "resource": resource,
                    "context": context
                }
            )
            
            if has_permission:
                return True, "Permission granted"
            else:
                return False, "Permission denied"
                
        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            self.audit_logger.log_error_event(
                action="permission_check_error",
                error_message=str(e),
                details={"permission": permission, "resource": resource},
                severity=AuditSeverity.HIGH
            )
            return False, "Permission check error"
    
    async def secure_action(
        self,
        token: str,
        action: str,
        permission: str,
        resource: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[str], str]:
        """Perform a security-checked action"""
        try:
            # Check permission
            has_permission, perm_message = await self.check_permission(
                token, permission, resource
            )
            
            if not has_permission:
                return False, None, perm_message
            
            # Get user info
            valid, payload, _ = await self.validate_session(token)
            user_id = payload.get("user_id") if payload else None
            
            # Log user action
            self.audit_logger.log_user_action_event(
                action=action,
                user_id=user_id,
                resource=resource,
                result="success",
                details=details,
                session_id=token
            )
            
            return True, user_id, "Action authorized"
            
        except Exception as e:
            logger.error(f"Error in secure action: {e}")
            return False, None, str(e)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            return self.encryption_manager.encrypt_data(data)
        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            raise
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            decrypted_bytes = self.encryption_manager.decrypt_data(encrypted_data)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            raise
    
    def create_user_account(
        self,
        admin_token: str,
        username: str,
        password: str,
        email: Optional[str] = None,
        is_admin: bool = False,
        permissions: Optional[List[str]] = None
    ) -> Tuple[bool, Optional[str], str]:
        """Create new user account (admin only)"""
        try:
            # Check admin permission
            valid, payload, message = self.auth_manager.validate_token(admin_token)
            if not valid:
                return False, None, "Invalid admin token"
            
            admin_user_id = payload.get("user_id")
            if not self.authz_manager.check_permission(admin_user_id, "user_management"):
                self.audit_logger.log_authorization_event(
                    action="create_user_denied",
                    user_id=admin_user_id,
                    result="failure",
                    details={"target_username": username}
                )
                return False, None, "Insufficient permissions"
            
            # Create user
            success, user_id = self.auth_manager.create_user(
                username, password, email, is_admin, permissions
            )
            
            if success:
                # Assign default role
                if not is_admin:
                    self.authz_manager.assign_role(user_id, "basic_user")
                else:
                    self.authz_manager.assign_role(user_id, "administrator")
                
                # Log user creation
                self.audit_logger.log_system_change_event(
                    action="user_created",
                    resource=f"user:{username}",
                    user_id=admin_user_id,
                    result="success",
                    details={
                        "new_user_id": user_id,
                        "username": username,
                        "is_admin": is_admin
                    }
                )
                
                return True, user_id, "User created successfully"
            else:
                return False, None, user_id  # user_id contains error message
                
        except Exception as e:
            logger.error(f"Error creating user account: {e}")
            return False, None, str(e)
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get comprehensive security status"""
        try:
            auth_stats = self.auth_manager.get_auth_stats()
            audit_stats = self.audit_logger.get_audit_statistics(days=7)
            encryption_status = self.encryption_manager.get_encryption_status()
            
            return {
                "security_enabled": self.security_enabled,
                "authentication": {
                    "enabled": self.require_authentication,
                    "stats": auth_stats
                },
                "authorization": {
                    "enabled": self.enforce_permissions,
                    "total_roles": len(self.authz_manager.roles),
                    "total_permissions": len(self.authz_manager.permissions)
                },
                "encryption": encryption_status,
                "audit": {
                    "enabled": self.audit_all_actions,
                    "recent_stats": audit_stats
                },
                "active_sessions": len(self.active_sessions),
                "system_health": "healthy"
            }
            
        except Exception as e:
            logger.error(f"Error getting security status: {e}")
            return {"error": str(e)}
    
    def logout_user(self, token: str) -> Tuple[bool, str]:
        """Logout user and revoke token"""
        try:
            # Get user info before logout
            valid, payload, _ = self.auth_manager.validate_token(token)
            user_id = payload.get("user_id") if payload else None
            username = payload.get("username") if payload else None
            
            # Revoke token
            success, message = self.auth_manager.revoke_token(token)
            
            # Remove from active sessions
            if token in self.active_sessions:
                del self.active_sessions[token]
            
            # Log logout
            if user_id:
                self.audit_logger.log_authentication_event(
                    action="logout",
                    user_id=user_id,
                    result="success",
                    details={"username": username}
                )
            
            return success, message
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return False, str(e)
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions and tokens"""
        try:
            # Clean up expired tokens
            self.auth_manager.cleanup_expired_tokens()
            
            # Clean up old audit logs
            self.audit_logger.cleanup_old_logs()
            
            # Remove expired sessions from tracking
            current_time = datetime.now()
            expired_tokens = []
            
            for token, session_info in self.active_sessions.items():
                # Check if token is still valid
                valid, _, _ = self.auth_manager.validate_token(token)
                if not valid:
                    expired_tokens.append(token)
            
            for token in expired_tokens:
                del self.active_sessions[token]
            
            if expired_tokens:
                logger.info(f"Cleaned up {len(expired_tokens)} expired sessions")
                
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")
    
    def get_user_security_summary(self, token: str) -> Dict[str, Any]:
        """Get security summary for current user"""
        try:
            # Validate session
            valid, payload, message = self.auth_manager.validate_token(token)
            if not valid:
                return {"error": message}
            
            user_id = payload.get("user_id")
            username = payload.get("username")
            
            # Get authorization summary
            authz_summary = self.authz_manager.get_authorization_summary(user_id)
            
            # Get session info
            session_info = self.active_sessions.get(token, {})
            
            return {
                "user_id": user_id,
                "username": username,
                "session": {
                    "login_time": session_info.get("login_time", "").isoformat() if session_info.get("login_time") else "",
                    "source_ip": session_info.get("source_ip"),
                    "user_agent": session_info.get("user_agent")
                },
                "authorization": authz_summary,
                "security_level": "high" if authz_summary.get("is_admin") else "standard"
            }
            
        except Exception as e:
            logger.error(f"Error getting user security summary: {e}")
            return {"error": str(e)}
    
    def shutdown(self):
        """Shutdown security manager"""
        try:
            # Shutdown audit logger
            self.audit_logger.shutdown()
            
            # Log shutdown
            logger.info("Security manager shutdown")
            
        except Exception as e:
            logger.error(f"Error during security manager shutdown: {e}")
