# JARVIS AI Assistant Configuration
# Copy this file to config.yaml and customize your settings

# AI Model Configuration
ai:
  # Primary AI provider (openai, anthropic, local)
  provider: "openai"
  
  # OpenAI Configuration
  openai:
    api_key: "your-openai-api-key-here"
    model: "gpt-4"
    temperature: 0.7
    max_tokens: 2000
    
  # Anthropic Configuration
  anthropic:
    api_key: "your-anthropic-api-key-here"
    model: "claude-3-sonnet-20240229"
    temperature: 0.7
    max_tokens: 2000
    
  # Local Model Configuration
  local:
    model_path: "models/local-model"
    device: "auto"  # auto, cpu, cuda

# Voice Configuration
voice:
  # Speech Recognition
  recognition:
    provider: "whisper"  # whisper, azure, google
    language: "en-US"
    energy_threshold: 300
    pause_threshold: 0.8
    
  # Text-to-Speech
  synthesis:
    provider: "pyttsx3"  # pyttsx3, elevenlabs, azure
    voice_id: "default"
    rate: 200
    volume: 0.9
    
  # ElevenLabs Configuration
  elevenlabs:
    api_key: "your-elevenlabs-api-key-here"
    voice_id: "21m00Tcm4TlvDq8ikWAM"
    
  # Azure Speech Configuration
  azure:
    api_key: "your-azure-speech-key-here"
    region: "eastus"
    voice_name: "en-US-JennyNeural"

# System Integration
system:
  # File Operations
  file_operations:
    enabled: true
    safe_mode: true  # Require confirmation for destructive operations
    allowed_extensions: [".txt", ".md", ".json", ".yaml", ".py", ".js"]
    restricted_paths: ["C:\\Windows", "C:\\Program Files"]
    
  # Application Control
  app_control:
    enabled: true
    safe_mode: true
    allowed_apps: ["notepad", "calculator", "chrome", "firefox"]
    
  # System Commands
  system_commands:
    enabled: false  # Disabled by default for security
    safe_mode: true
    allowed_commands: ["dir", "ls", "pwd", "date", "time"]

# Web Integration
web:
  # Browser Automation
  browser:
    provider: "selenium"  # selenium, playwright
    headless: false
    default_browser: "chrome"
    timeout: 30
    
  # Web Search
  search:
    provider: "google"  # google, bing, duckduckgo
    api_key: "your-search-api-key-here"
    results_limit: 10
    
  # Web Scraping
  scraping:
    enabled: true
    respect_robots_txt: true
    delay_between_requests: 1.0
    user_agent: "JARVIS-AI-Assistant/1.0"

# Memory Configuration
memory:
  # Vector Database
  vector_db:
    provider: "chromadb"  # chromadb, pinecone, weaviate
    persist_directory: "data/memory"
    collection_name: "jarvis_memory"
    
  # Conversation History
  conversation:
    max_history: 100
    persist: true
    file_path: "data/conversations.json"
    
  # Personal Information
  personal:
    enabled: true
    file_path: "data/personal.json"
    encryption: true

# User Interface
interface:
  # CLI Configuration
  cli:
    prompt: "JARVIS> "
    colors: true
    auto_complete: true
    
  # GUI Configuration
  gui:
    theme: "dark"
    window_size: [1200, 800]
    always_on_top: false
    
  # Voice Interface
  voice_interface:
    wake_word: "jarvis"
    continuous_listening: false
    beep_on_activation: true

# Security
security:
  # Encryption
  encryption:
    enabled: true
    key_file: "data/encryption.key"
    
  # Permissions
  permissions:
    require_confirmation: true
    log_all_actions: true
    
  # API Rate Limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 60

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file_path: "logs/jarvis.log"
  max_file_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Personal Assistant Features
assistant:
  # Calendar Integration
  calendar:
    enabled: false
    provider: "google"  # google, outlook
    api_key: "your-calendar-api-key-here"
    
  # Email Integration
  email:
    enabled: false
    provider: "gmail"  # gmail, outlook
    api_key: "your-email-api-key-here"
    
  # Task Management
  tasks:
    enabled: true
    file_path: "data/tasks.json"
    
  # Reminders
  reminders:
    enabled: true
    file_path: "data/reminders.json"

# Development Settings
development:
  debug_mode: false
  test_mode: false
  mock_apis: false
  verbose_logging: false
