[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "jarvis-ai-assistant"
version = "0.1.0"
description = "A highly intelligent, JARVIS-like AI assistant with voice control and system automation"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "JARVIS Development Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.9"
dependencies = [
    "openai>=1.0.0",
    "langchain>=0.1.0",
    "SpeechRecognition>=3.10.0",
    "pyttsx3>=2.90",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "psutil>=5.9.0",
    "pyautogui>=0.9.54",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]
voice = [
    "openai-whisper>=20231117",
    "elevenlabs>=0.2.0",
    "azure-cognitiveservices-speech>=1.30.0",
]
web = [
    "selenium>=4.15.0",
    "playwright>=1.40.0",
    "scrapy>=2.11.0",
]
gui = [
    "customtkinter>=5.2.0",
    "PyQt6>=6.5.0",
    "aiohttp>=3.8.0",
    "aiohttp-cors>=0.7.0",
    "python-socketio>=5.8.0",
]
ai = [
    "anthropic>=0.7.0",
    "transformers>=4.30.0",
    "torch>=2.0.0",
    "chromadb>=0.4.0",
]

[project.scripts]
jarvis = "jarvis.cli:main"
jarvis-voice = "jarvis.voice:main"
jarvis-gui = "jarvis.gui:main"

[project.urls]
Homepage = "https://github.com/jarvis-ai/jarvis"
Repository = "https://github.com/jarvis-ai/jarvis.git"
Documentation = "https://jarvis-ai.readthedocs.io"
"Bug Tracker" = "https://github.com/jarvis-ai/jarvis/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["jarvis*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
