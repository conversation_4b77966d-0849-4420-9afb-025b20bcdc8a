"""
Search Engine Integration for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional
import requests
from urllib.parse import quote_plus
import json
from datetime import datetime

from ...config import config

logger = logging.getLogger(__name__)


class SearchEngine:
    """Multi-provider search engine integration"""
    
    def __init__(self):
        """Initialize search engine"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # API configurations
        self.google_api_key = config.get_api_key("google_search")
        self.google_cse_id = config.get("web.search.google_cse_id")
        self.bing_api_key = config.get_api_key("bing_search")
        
        logger.info("Search engine initialized")
    
    def search(
        self, 
        query: str, 
        provider: str = "auto",
        num_results: int = 10,
        search_type: str = "web"
    ) -> Dict[str, Any]:
        """
        Perform web search
        
        Args:
            query: Search query
            provider: Search provider (auto, google, bing, duckduckgo)
            num_results: Number of results to return
            search_type: Type of search (web, images, news, videos)
            
        Returns:
            Dictionary with search results
        """
        try:
            if provider == "auto":
                # Choose best available provider
                if self.google_api_key and self.google_cse_id:
                    provider = "google"
                elif self.bing_api_key:
                    provider = "bing"
                else:
                    provider = "duckduckgo"
            
            if provider == "google":
                return self._search_google(query, num_results, search_type)
            elif provider == "bing":
                return self._search_bing(query, num_results, search_type)
            elif provider == "duckduckgo":
                return self._search_duckduckgo(query, num_results)
            else:
                return {
                    "success": False,
                    "error": f"Unknown search provider: {provider}"
                }
                
        except Exception as e:
            logger.error(f"Error searching with {provider}: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "provider": provider
            }
    
    def _search_google(self, query: str, num_results: int, search_type: str) -> Dict[str, Any]:
        """Search using Google Custom Search API"""
        try:
            if not self.google_api_key or not self.google_cse_id:
                return {
                    "success": False,
                    "error": "Google API key or Custom Search Engine ID not configured"
                }
            
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.google_api_key,
                "cx": self.google_cse_id,
                "q": query,
                "num": min(num_results, 10)
            }
            
            # Add search type specific parameters
            if search_type == "images":
                params["searchType"] = "image"
            elif search_type == "news":
                params["tbm"] = "nws"
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            results = []
            for item in data.get("items", []):
                result = {
                    "title": item.get("title", ""),
                    "url": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "display_link": item.get("displayLink", "")
                }
                
                # Add image-specific data
                if search_type == "images" and "image" in item:
                    result.update({
                        "image_url": item["link"],
                        "thumbnail_url": item["image"].get("thumbnailLink", ""),
                        "image_width": item["image"].get("width"),
                        "image_height": item["image"].get("height")
                    })
                
                results.append(result)
            
            search_info = data.get("searchInformation", {})
            
            return {
                "success": True,
                "query": query,
                "provider": "google",
                "search_type": search_type,
                "results": results,
                "total_results": search_info.get("totalResults", "0"),
                "search_time": float(search_info.get("searchTime", "0")),
                "searched_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "provider": "google"
            }
    
    def _search_bing(self, query: str, num_results: int, search_type: str) -> Dict[str, Any]:
        """Search using Bing Search API"""
        try:
            if not self.bing_api_key:
                return {
                    "success": False,
                    "error": "Bing API key not configured"
                }
            
            # Choose endpoint based on search type
            if search_type == "images":
                url = "https://api.bing.microsoft.com/v7.0/images/search"
            elif search_type == "news":
                url = "https://api.bing.microsoft.com/v7.0/news/search"
            elif search_type == "videos":
                url = "https://api.bing.microsoft.com/v7.0/videos/search"
            else:
                url = "https://api.bing.microsoft.com/v7.0/search"
            
            headers = {
                "Ocp-Apim-Subscription-Key": self.bing_api_key
            }
            
            params = {
                "q": query,
                "count": min(num_results, 50),
                "mkt": "en-US"
            }
            
            response = self.session.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            results = []
            
            if search_type == "images":
                for item in data.get("value", []):
                    results.append({
                        "title": item.get("name", ""),
                        "url": item.get("contentUrl", ""),
                        "thumbnail_url": item.get("thumbnailUrl", ""),
                        "image_width": item.get("width"),
                        "image_height": item.get("height"),
                        "host_page_url": item.get("hostPageUrl", "")
                    })
            elif search_type == "news":
                for item in data.get("value", []):
                    results.append({
                        "title": item.get("name", ""),
                        "url": item.get("url", ""),
                        "snippet": item.get("description", ""),
                        "published_at": item.get("datePublished", ""),
                        "provider": item.get("provider", [{}])[0].get("name", "")
                    })
            else:
                # Web search
                for item in data.get("webPages", {}).get("value", []):
                    results.append({
                        "title": item.get("name", ""),
                        "url": item.get("url", ""),
                        "snippet": item.get("snippet", ""),
                        "display_url": item.get("displayUrl", "")
                    })
            
            return {
                "success": True,
                "query": query,
                "provider": "bing",
                "search_type": search_type,
                "results": results,
                "searched_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "provider": "bing"
            }
    
    def _search_duckduckgo(self, query: str, num_results: int) -> Dict[str, Any]:
        """Search using DuckDuckGo Instant Answer API"""
        try:
            # DuckDuckGo Instant Answer API
            url = "https://api.duckduckgo.com/"
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            results = []
            
            # Abstract (main answer)
            if data.get("Abstract"):
                results.append({
                    "title": data.get("AbstractText", ""),
                    "url": data.get("AbstractURL", ""),
                    "snippet": data.get("Abstract", ""),
                    "source": data.get("AbstractSource", ""),
                    "type": "abstract"
                })
            
            # Related topics
            for topic in data.get("RelatedTopics", []):
                if isinstance(topic, dict) and topic.get("Text"):
                    results.append({
                        "title": topic.get("Text", "").split(" - ")[0],
                        "url": topic.get("FirstURL", ""),
                        "snippet": topic.get("Text", ""),
                        "type": "related"
                    })
            
            # Answer (direct answer)
            if data.get("Answer"):
                results.insert(0, {
                    "title": "Direct Answer",
                    "url": "",
                    "snippet": data.get("Answer", ""),
                    "type": "answer"
                })
            
            # Limit results
            results = results[:num_results]
            
            return {
                "success": True,
                "query": query,
                "provider": "duckduckgo",
                "search_type": "web",
                "results": results,
                "answer_type": data.get("AnswerType", ""),
                "searched_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "provider": "duckduckgo"
            }
    
    def search_news(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """Search for news articles"""
        return self.search(query, "auto", num_results, "news")
    
    def search_images(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """Search for images"""
        return self.search(query, "auto", num_results, "images")
    
    def search_videos(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """Search for videos"""
        return self.search(query, "auto", num_results, "videos")
    
    def get_trending(self, provider: str = "auto", region: str = "US") -> Dict[str, Any]:
        """Get trending topics"""
        try:
            if provider == "auto":
                provider = "google" if self.google_api_key else "bing"
            
            if provider == "google":
                return self._get_google_trends(region)
            elif provider == "bing":
                return self._get_bing_trending(region)
            else:
                return {
                    "success": False,
                    "error": f"Trending not supported for provider: {provider}"
                }
                
        except Exception as e:
            logger.error(f"Error getting trending topics: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": provider
            }
    
    def _get_google_trends(self, region: str) -> Dict[str, Any]:
        """Get Google Trends data"""
        try:
            # Note: This would require Google Trends API or web scraping
            # For now, return a placeholder
            return {
                "success": False,
                "error": "Google Trends API integration not implemented",
                "note": "Consider using pytrends library for Google Trends data"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_bing_trending(self, region: str) -> Dict[str, Any]:
        """Get Bing trending topics"""
        try:
            if not self.bing_api_key:
                return {
                    "success": False,
                    "error": "Bing API key not configured"
                }
            
            url = "https://api.bing.microsoft.com/v7.0/news/trendingtopics"
            headers = {
                "Ocp-Apim-Subscription-Key": self.bing_api_key
            }
            params = {
                "mkt": f"{region.lower()}-{region.upper()}"
            }
            
            response = self.session.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            trending = []
            for item in data.get("value", []):
                trending.append({
                    "name": item.get("name", ""),
                    "url": item.get("webSearchUrl", ""),
                    "image_url": item.get("image", {}).get("url", ""),
                    "query": item.get("query", {}).get("text", "")
                })
            
            return {
                "success": True,
                "provider": "bing",
                "region": region,
                "trending": trending,
                "retrieved_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "bing"
            }
    
    def suggest_queries(self, query: str, provider: str = "auto") -> Dict[str, Any]:
        """Get query suggestions"""
        try:
            if provider == "auto":
                provider = "google"
            
            if provider == "google":
                return self._get_google_suggestions(query)
            else:
                return {
                    "success": False,
                    "error": f"Suggestions not supported for provider: {provider}"
                }
                
        except Exception as e:
            logger.error(f"Error getting query suggestions: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def _get_google_suggestions(self, query: str) -> Dict[str, Any]:
        """Get Google search suggestions"""
        try:
            url = "http://suggestqueries.google.com/complete/search"
            params = {
                "client": "firefox",
                "q": query
            }
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            # Google returns JSON array
            suggestions = response.json()[1] if response.json() else []
            
            return {
                "success": True,
                "query": query,
                "suggestions": suggestions,
                "provider": "google"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get search engine statistics"""
        return {
            "google_api_configured": bool(self.google_api_key and self.google_cse_id),
            "bing_api_configured": bool(self.bing_api_key),
            "session_cookies": len(self.session.cookies)
        }
