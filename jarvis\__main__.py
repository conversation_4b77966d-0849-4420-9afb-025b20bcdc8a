"""
Main entry point for JARVIS AI Assistant
"""

import asyncio
import sys
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from jarvis.config import config
from jarvis.utils.logging import setup_logging


def setup_argument_parser():
    """Set up command line argument parser"""
    parser = argparse.ArgumentParser(
        description="JARVIS - Advanced AI Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m jarvis                    # Start CLI interface
  python -m jarvis --interface cli   # Start CLI interface
  python -m jarvis --interface voice # Start voice interface
  python -m jarvis --interface gui   # Start GUI interface
  python -m jarvis --config custom.yaml  # Use custom config file
        """
    )
    
    parser.add_argument(
        "--interface", "-i",
        choices=["cli", "voice", "gui"],
        default="cli",
        help="Interface to use (default: cli)"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Path to configuration file"
    )
    
    parser.add_argument(
        "--debug", "-d",
        action="store_true",
        help="Enable debug logging"
    )
    
    parser.add_argument(
        "--version", "-v",
        action="version",
        version="JARVIS AI Assistant v0.1.0"
    )
    
    return parser


async def start_cli_interface():
    """Start the CLI interface"""
    try:
        from jarvis.interfaces.cli import CLIInterface
        cli = CLIInterface()
        await cli.run()
    except ImportError as e:
        print(f"Failed to import CLI interface: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Failed to start CLI interface: {e}")
        sys.exit(1)


async def start_voice_interface():
    """Start the voice interface"""
    try:
        from jarvis.integrations.voice import VoiceInterface
        voice = VoiceInterface()

        # Start continuous listening mode
        print("Starting JARVIS voice interface...")
        print("Say 'JARVIS' or 'Hey JARVIS' to activate voice commands")
        print("Press Ctrl+C to exit")

        await voice.start_listening(continuous=True)
    except ImportError as e:
        print(f"Voice interface not yet implemented: {e}")
        print("Please use the CLI interface for now: python -m jarvis --interface cli")
        sys.exit(1)
    except Exception as e:
        print(f"Failed to start voice interface: {e}")
        sys.exit(1)


async def start_gui_interface():
    """Start the GUI interface"""
    try:
        from jarvis.interfaces.gui import WebInterface

        print("Starting JARVIS GUI interface...")
        print("Web interface will be available at http://localhost:8080")

        gui = WebInterface()
        await gui.start()
    except ImportError as e:
        print(f"GUI interface not yet implemented: {e}")
        print("Please use the CLI interface for now: python -m jarvis --interface cli")
        sys.exit(1)
    except Exception as e:
        print(f"Failed to start GUI interface: {e}")
        sys.exit(1)


async def main():
    """Main entry point"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Set up logging
    log_level = "DEBUG" if args.debug else config.get("logging.level", "INFO")
    setup_logging(level=log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("Starting JARVIS AI Assistant")
    
    # Load custom config if specified
    if args.config:
        config.config_path = args.config
        config.reload()
        logger.info(f"Loaded custom configuration: {args.config}")
    
    # Start the appropriate interface
    try:
        if args.interface == "cli":
            await start_cli_interface()
        elif args.interface == "voice":
            await start_voice_interface()
        elif args.interface == "gui":
            await start_gui_interface()
        else:
            print(f"Unknown interface: {args.interface}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("Received interrupt signal. Shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    
    logger.info("JARVIS AI Assistant stopped")


if __name__ == "__main__":
    asyncio.run(main())
