"""
Advanced Task Handlers for JARVIS AI Assistant
"""

import asyncio
import re
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import math

from .base import TaskHandler, Task, TaskResult

logger = logging.getLogger(__name__)


class MathTaskHandler(TaskHandler):
    """Handler for mathematical calculations"""
    
    @property
    def name(self) -> str:
        return "math"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a math task"""
        return task.command.startswith("math.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a math calculation task"""
        try:
            command = task.command.replace("math.", "")
            
            if command == "calculate":
                return await self._calculate(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown math command: {command}"
                )
                
        except Exception as e:
            logger.error(f"Math task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _calculate(self, task: Task) -> TaskResult:
        """Perform mathematical calculation"""
        try:
            # Get expression from parameters
            expression = task.parameters.get("expression")
            if not expression:
                # Try to extract from original text
                original_text = task.parameters.get("original_text", "")
                expression = self._extract_math_expression(original_text)
            
            if not expression:
                return TaskResult(
                    success=False,
                    error="No mathematical expression found"
                )
            
            # Clean and validate expression
            cleaned_expr = self._clean_expression(expression)
            if not self._is_safe_expression(cleaned_expr):
                return TaskResult(
                    success=False,
                    error="Invalid or unsafe mathematical expression"
                )
            
            # Calculate result
            result = eval(cleaned_expr)
            
            return TaskResult(
                success=True,
                data={
                    "expression": expression,
                    "result": result,
                    "formatted_result": f"{expression} = {result}"
                },
                metadata={
                    "calculation_type": "basic_arithmetic",
                    "expression_length": len(expression)
                }
            )
            
        except ZeroDivisionError:
            return TaskResult(
                success=False,
                error="Division by zero is not allowed"
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error=f"Calculation error: {str(e)}"
            )
    
    def _extract_math_expression(self, text: str) -> Optional[str]:
        """Extract mathematical expression from text"""
        # Look for mathematical expressions
        patterns = [
            r'(\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*)',
            r'(\d+\s+(?:plus|minus|times|divided by)\s+\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                expr = match.group(1)
                # Convert word operations to symbols
                expr = expr.replace(" plus ", " + ")
                expr = expr.replace(" minus ", " - ")
                expr = expr.replace(" times ", " * ")
                expr = expr.replace(" divided by ", " / ")
                return expr
        
        return None
    
    def _clean_expression(self, expression: str) -> str:
        """Clean mathematical expression"""
        # Remove extra spaces
        cleaned = re.sub(r'\s+', '', expression)
        
        # Ensure only valid characters
        if not re.match(r'^[\d\+\-\*\/\.\(\)]+$', cleaned):
            raise ValueError("Invalid characters in expression")
        
        return cleaned
    
    def _is_safe_expression(self, expression: str) -> bool:
        """Check if expression is safe to evaluate"""
        # Only allow basic arithmetic operations
        allowed_chars = set('0123456789+-*/.() ')
        return all(c in allowed_chars for c in expression)


class WeatherTaskHandler(TaskHandler):
    """Handler for weather-related tasks"""
    
    @property
    def name(self) -> str:
        return "weather"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a weather task"""
        return task.command.startswith("weather.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a weather task"""
        try:
            command = task.command.replace("weather.", "")
            
            if command == "current":
                return await self._get_current_weather(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown weather command: {command}"
                )
                
        except Exception as e:
            logger.error(f"Weather task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _get_current_weather(self, task: Task) -> TaskResult:
        """Get current weather (mock implementation)"""
        location = task.parameters.get("location", "your location")
        
        # Mock weather data
        mock_weather = {
            "location": location,
            "temperature": "22°C",
            "condition": "Partly cloudy",
            "humidity": "65%",
            "wind": "10 km/h",
            "description": f"The weather in {location} is partly cloudy with a temperature of 22°C."
        }
        
        return TaskResult(
            success=True,
            data=mock_weather,
            metadata={
                "source": "mock_data",
                "timestamp": datetime.now().isoformat()
            }
        )


class FileTaskHandler(TaskHandler):
    """Enhanced handler for file operations using FileManager"""

    def __init__(self):
        """Initialize file task handler"""
        try:
            from ...integrations.system import FileManager
            self.file_manager = FileManager()
        except ImportError:
            logger.warning("FileManager not available, using basic file operations")
            self.file_manager = None

    @property
    def name(self) -> str:
        return "file"

    def can_handle(self, task: Task) -> bool:
        """Check if this is a file task"""
        return task.command.startswith("file.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute a file operation task"""
        try:
            # Extract operation from command
            command_parts = task.command.split(".")
            if len(command_parts) < 2:
                return TaskResult(
                    success=False,
                    error="Invalid file command format"
                )

            operation = command_parts[1]

            # Route to appropriate operation
            if operation == "list":
                return await self._list_files(task)
            elif operation == "read":
                return await self._read_file(task)
            elif operation == "write":
                return await self._write_file(task)
            elif operation == "copy":
                return await self._copy_file(task)
            elif operation == "move":
                return await self._move_file(task)
            elif operation == "delete":
                return await self._delete_file(task)
            elif operation == "create_dir":
                return await self._create_directory(task)
            elif operation == "info":
                return await self._get_file_info(task)
            elif operation == "search":
                return await self._search_files(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown file operation: {operation}"
                )

        except Exception as e:
            logger.error(f"File task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _list_files(self, task: Task) -> TaskResult:
        """List files in directory"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        path = task.parameters.get("path", ".")
        detailed = task.parameters.get("detailed", False)

        result = self.file_manager.list_directory(path, detailed)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _read_file(self, task: Task) -> TaskResult:
        """Read file contents"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        path = task.parameters.get("path")
        if not path:
            return TaskResult(success=False, error="No file path specified")

        encoding = task.parameters.get("encoding", "utf-8")
        result = self.file_manager.read_file(path, encoding)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _write_file(self, task: Task) -> TaskResult:
        """Write content to file"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        path = task.parameters.get("path")
        content = task.parameters.get("content", "")

        if not path:
            return TaskResult(success=False, error="No file path specified")

        encoding = task.parameters.get("encoding", "utf-8")
        create_dirs = task.parameters.get("create_dirs", True)
        backup = task.parameters.get("backup", True)

        result = self.file_manager.write_file(path, content, encoding, create_dirs, backup)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _copy_file(self, task: Task) -> TaskResult:
        """Copy file"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        source = task.parameters.get("source")
        destination = task.parameters.get("destination")

        if not source or not destination:
            return TaskResult(success=False, error="Source and destination paths required")

        overwrite = task.parameters.get("overwrite", False)
        result = self.file_manager.copy_file(source, destination, overwrite)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _move_file(self, task: Task) -> TaskResult:
        """Move file"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        source = task.parameters.get("source")
        destination = task.parameters.get("destination")

        if not source or not destination:
            return TaskResult(success=False, error="Source and destination paths required")

        overwrite = task.parameters.get("overwrite", False)
        result = self.file_manager.move_file(source, destination, overwrite)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _delete_file(self, task: Task) -> TaskResult:
        """Delete file"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        path = task.parameters.get("path")
        if not path:
            return TaskResult(success=False, error="No file path specified")

        permanent = task.parameters.get("permanent", False)
        result = self.file_manager.delete_file(path, permanent)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _create_directory(self, task: Task) -> TaskResult:
        """Create directory"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        path = task.parameters.get("path")
        if not path:
            return TaskResult(success=False, error="No directory path specified")

        parents = task.parameters.get("parents", True)
        result = self.file_manager.create_directory(path, parents)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _get_file_info(self, task: Task) -> TaskResult:
        """Get file information"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        path = task.parameters.get("path")
        if not path:
            return TaskResult(success=False, error="No file path specified")

        result = self.file_manager.get_file_info(path)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _search_files(self, task: Task) -> TaskResult:
        """Search for files"""
        if not self.file_manager:
            return TaskResult(success=False, error="File manager not available")

        directory = task.parameters.get("directory", ".")
        pattern = task.parameters.get("pattern", "*")
        recursive = task.parameters.get("recursive", True)
        include_content = task.parameters.get("include_content", False)
        max_results = task.parameters.get("max_results", 100)

        result = self.file_manager.search_files(
            directory, pattern, recursive, include_content, max_results
        )

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )


class WebTaskHandler(TaskHandler):
    """Enhanced handler for web-related tasks"""

    def __init__(self):
        """Initialize web task handler"""
        try:
            from ...integrations.web import WebBrowser, SearchEngine, WebScraper
            self.browser = WebBrowser()
            self.search_engine = SearchEngine()
            self.scraper = WebScraper()
        except ImportError:
            logger.warning("Web integration modules not available")
            self.browser = None
            self.search_engine = None
            self.scraper = None

    @property
    def name(self) -> str:
        return "web"

    def can_handle(self, task: Task) -> bool:
        """Check if this is a web task"""
        return task.command.startswith("web.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute a web task"""
        try:
            command_parts = task.command.split(".")
            if len(command_parts) < 2:
                return TaskResult(
                    success=False,
                    error="Invalid web command format"
                )

            operation = command_parts[1]

            if operation == "search":
                return await self._search_web(task)
            elif operation == "browse":
                return await self._browse_page(task)
            elif operation == "scrape":
                return await self._scrape_content(task)
            elif operation == "download":
                return await self._download_file(task)
            elif operation == "screenshot":
                return await self._take_screenshot(task)
            elif operation == "extract":
                return await self._extract_content(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown web operation: {operation}"
                )

        except Exception as e:
            logger.error(f"Web task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _search_web(self, task: Task) -> TaskResult:
        """Perform web search"""
        if not self.search_engine:
            return TaskResult(success=False, error="Search engine not available")

        query = task.parameters.get("query", "")
        if not query:
            return TaskResult(success=False, error="No search query provided")

        provider = task.parameters.get("provider", "auto")
        num_results = task.parameters.get("num_results", 10)
        search_type = task.parameters.get("search_type", "web")

        result = self.search_engine.search(query, provider, num_results, search_type)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _browse_page(self, task: Task) -> TaskResult:
        """Browse a web page"""
        if not self.browser:
            return TaskResult(success=False, error="Web browser not available")

        url = task.parameters.get("url")
        if not url:
            return TaskResult(success=False, error="No URL provided")

        use_selenium = task.parameters.get("use_selenium", False)
        result = self.browser.get_page(url, use_selenium)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _scrape_content(self, task: Task) -> TaskResult:
        """Scrape content from web page"""
        if not self.scraper:
            return TaskResult(success=False, error="Web scraper not available")

        url = task.parameters.get("url")
        if not url:
            return TaskResult(success=False, error="No URL provided")

        content_type = task.parameters.get("content_type", "auto")
        result = self.scraper.extract_content(url, content_type)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _download_file(self, task: Task) -> TaskResult:
        """Download file from URL"""
        if not self.browser:
            return TaskResult(success=False, error="Web browser not available")

        url = task.parameters.get("url")
        if not url:
            return TaskResult(success=False, error="No URL provided")

        filename = task.parameters.get("filename")
        result = self.browser.download_file(url, filename)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _take_screenshot(self, task: Task) -> TaskResult:
        """Take screenshot of web page"""
        if not self.browser:
            return TaskResult(success=False, error="Web browser not available")

        url = task.parameters.get("url")
        if not url:
            return TaskResult(success=False, error="No URL provided")

        filename = task.parameters.get("filename")
        result = self.browser.take_screenshot(url, filename)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _extract_content(self, task: Task) -> TaskResult:
        """Extract article content from web page"""
        if not self.scraper:
            return TaskResult(success=False, error="Web scraper not available")

        url = task.parameters.get("url")
        if not url:
            return TaskResult(success=False, error="No URL provided")

        result = self.scraper.extract_article(url)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )


class ReminderTaskHandler(TaskHandler):
    """Handler for reminder tasks"""

    @property
    def name(self) -> str:
        return "reminder"

    def can_handle(self, task: Task) -> bool:
        """Check if this is a reminder task"""
        return task.command.startswith("reminder.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute a reminder task"""
        try:
            command = task.command.replace("reminder.", "")

            if command == "create":
                return await self._create_reminder(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown reminder command: {command}"
                )

        except Exception as e:
            logger.error(f"Reminder task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _create_reminder(self, task: Task) -> TaskResult:
        """Create a new reminder"""
        message = task.parameters.get("message", "Reminder")
        reminder_time = task.parameters.get("reminder_time")
        reminder_date = task.parameters.get("reminder_date")

        reminder_data = {
            "message": message,
            "created_at": datetime.now().isoformat(),
            "reminder_time": reminder_time,
            "reminder_date": reminder_date,
            "status": "active"
        }

        return TaskResult(
            success=True,
            data=reminder_data,
            metadata={
                "reminder_type": "basic",
                "has_time": bool(reminder_time),
                "has_date": bool(reminder_date)
            }
        )


class ApplicationTaskHandler(TaskHandler):
    """Handler for application control tasks"""

    def __init__(self):
        """Initialize application task handler"""
        try:
            from ...integrations.system import ApplicationController
            self.app_controller = ApplicationController()
        except ImportError:
            logger.warning("ApplicationController not available")
            self.app_controller = None

    @property
    def name(self) -> str:
        return "app"

    def can_handle(self, task: Task) -> bool:
        """Check if this is an app task"""
        return task.command.startswith("app.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute an application control task"""
        try:
            if not self.app_controller:
                return TaskResult(
                    success=False,
                    error="Application controller not available"
                )

            command_parts = task.command.split(".")
            if len(command_parts) < 2:
                return TaskResult(
                    success=False,
                    error="Invalid app command format"
                )

            operation = command_parts[1]

            if operation == "launch":
                return await self._launch_app(task)
            elif operation == "close":
                return await self._close_app(task)
            elif operation == "list":
                return await self._list_apps(task)
            elif operation == "info":
                return await self._get_app_info(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown app operation: {operation}"
                )

        except Exception as e:
            logger.error(f"Application task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _launch_app(self, task: Task) -> TaskResult:
        """Launch an application"""
        if not self.app_controller:
            return TaskResult(
                success=False,
                error="Application controller not available"
            )

        app_name = task.parameters.get("app_name")
        if not app_name:
            return TaskResult(success=False, error="No application name specified")

        args = task.parameters.get("args", [])
        wait_for_start = task.parameters.get("wait_for_start", True)
        timeout = task.parameters.get("timeout", 30)

        result = self.app_controller.launch_application(app_name, args, wait_for_start, timeout)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _close_app(self, task: Task) -> TaskResult:
        """Close an application"""
        if not self.app_controller:
            return TaskResult(
                success=False,
                error="Application controller not available"
            )

        identifier = task.parameters.get("identifier")
        if not identifier:
            return TaskResult(success=False, error="No application identifier specified")

        force = task.parameters.get("force", False)
        result = self.app_controller.close_application(identifier, force)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _list_apps(self, task: Task) -> TaskResult:
        """List running applications"""
        if not self.app_controller:
            return TaskResult(
                success=False,
                error="Application controller not available"
            )

        filter_name = task.parameters.get("filter_name")
        result = self.app_controller.list_running_applications(filter_name)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _get_app_info(self, task: Task) -> TaskResult:
        """Get application information"""
        if not self.app_controller:
            return TaskResult(
                success=False,
                error="Application controller not available"
            )

        identifier = task.parameters.get("identifier")
        if not identifier:
            return TaskResult(success=False, error="No application identifier specified")

        result = self.app_controller.get_application_info(identifier)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )


class SystemMonitorTaskHandler(TaskHandler):
    """Handler for system monitoring tasks"""

    def __init__(self):
        """Initialize system monitor task handler"""
        try:
            from ...integrations.system import SystemMonitor
            self.system_monitor = SystemMonitor()
        except ImportError:
            logger.warning("SystemMonitor not available")
            self.system_monitor = None

    @property
    def name(self) -> str:
        return "monitor"

    def can_handle(self, task: Task) -> bool:
        """Check if this is a monitor task"""
        return task.command.startswith("monitor.") or task.command.startswith("system.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute a system monitoring task"""
        try:
            if not self.system_monitor:
                return TaskResult(
                    success=False,
                    error="System monitor not available"
                )

            # Handle both monitor.* and system.* commands
            command = task.command
            if command.startswith("monitor."):
                operation = command.replace("monitor.", "")
            elif command.startswith("system."):
                operation = command.replace("system.", "")
            else:
                return TaskResult(
                    success=False,
                    error="Invalid monitor command format"
                )

            if operation == "info":
                return await self._get_system_info(task)
            elif operation == "usage":
                return await self._get_current_usage(task)
            elif operation == "processes":
                return await self._get_top_processes(task)
            elif operation == "health":
                return await self._check_system_health(task)
            elif operation == "history":
                return await self._get_usage_history(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown monitor operation: {operation}"
                )

        except Exception as e:
            logger.error(f"System monitor task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _get_system_info(self, task: Task) -> TaskResult:
        """Get system information"""
        if not self.system_monitor:
            return TaskResult(
                success=False,
                error="System monitor not available"
            )

        result = self.system_monitor.get_system_info()

        return TaskResult(
            success=result["success"],
            data=result.get("data"),
            error=result.get("error")
        )

    async def _get_current_usage(self, task: Task) -> TaskResult:
        """Get current system usage"""
        if not self.system_monitor:
            return TaskResult(
                success=False,
                error="System monitor not available"
            )

        result = self.system_monitor.get_current_usage()

        return TaskResult(
            success=result["success"],
            data=result.get("data"),
            error=result.get("error")
        )

    async def _get_top_processes(self, task: Task) -> TaskResult:
        """Get top processes"""
        if not self.system_monitor:
            return TaskResult(
                success=False,
                error="System monitor not available"
            )

        limit = task.parameters.get("limit", 10)
        sort_by = task.parameters.get("sort_by", "memory")

        result = self.system_monitor.get_top_processes(limit, sort_by)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _check_system_health(self, task: Task) -> TaskResult:
        """Check system health"""
        if not self.system_monitor:
            return TaskResult(
                success=False,
                error="System monitor not available"
            )

        result = self.system_monitor.check_system_health()

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _get_usage_history(self, task: Task) -> TaskResult:
        """Get usage history"""
        if not self.system_monitor:
            return TaskResult(
                success=False,
                error="System monitor not available"
            )

        hours = task.parameters.get("hours", 1)
        result = self.system_monitor.get_usage_history(hours)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )


class VoiceTaskHandler(TaskHandler):
    """Handler for voice interface tasks"""

    def __init__(self):
        """Initialize voice task handler"""
        try:
            from ...integrations.voice import VoiceInterface
            self.voice_interface = VoiceInterface()
        except ImportError:
            logger.warning("Voice integration modules not available")
            self.voice_interface = None

    @property
    def name(self) -> str:
        return "voice"

    def can_handle(self, task: Task) -> bool:
        """Check if this is a voice task"""
        return task.command.startswith("voice.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute a voice task"""
        try:
            if not self.voice_interface:
                return TaskResult(
                    success=False,
                    error="Voice interface not available"
                )

            command_parts = task.command.split(".")
            if len(command_parts) < 2:
                return TaskResult(
                    success=False,
                    error="Invalid voice command format"
                )

            operation = command_parts[1]

            if operation == "speak":
                return await self._speak_text(task)
            elif operation == "listen":
                return await self._listen_command(task)
            elif operation == "start_listening":
                return await self._start_listening(task)
            elif operation == "stop_listening":
                return await self._stop_listening(task)
            elif operation == "test":
                return await self._test_voice(task)
            elif operation == "status":
                return await self._get_voice_status(task)
            elif operation == "voices":
                return await self._get_voices(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown voice operation: {operation}"
                )

        except Exception as e:
            logger.error(f"Voice task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _speak_text(self, task: Task) -> TaskResult:
        """Speak text using TTS"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        text = task.parameters.get("text", "")
        if not text:
            return TaskResult(success=False, error="No text provided")

        save_file = task.parameters.get("save_file")
        result = await self.voice_interface.speak(text, save_file)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _listen_command(self, task: Task) -> TaskResult:
        """Listen for a single voice command"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        duration = task.parameters.get("duration", 5.0)

        # Use speech recognizer directly for single command
        result = await self.voice_interface.speech_recognizer.recognize_from_microphone(duration)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _start_listening(self, task: Task) -> TaskResult:
        """Start continuous voice listening"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        continuous = task.parameters.get("continuous", True)
        result = await self.voice_interface.start_listening(continuous)

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _stop_listening(self, task: Task) -> TaskResult:
        """Stop voice listening"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        result = await self.voice_interface.stop_listening()

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _test_voice(self, task: Task) -> TaskResult:
        """Test voice interface"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        result = await self.voice_interface.test_voice_interface()

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _get_voice_status(self, task: Task) -> TaskResult:
        """Get voice interface status"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        stats = self.voice_interface.get_stats()

        return TaskResult(
            success=True,
            data=stats
        )

    async def _get_voices(self, task: Task) -> TaskResult:
        """Get available voices"""
        if not self.voice_interface:
            return TaskResult(
                success=False,
                error="Voice interface not available"
            )

        result = await self.voice_interface.text_to_speech.get_voices()

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )


class GUITaskHandler(TaskHandler):
    """Handler for GUI interface tasks"""

    def __init__(self):
        """Initialize GUI task handler"""
        try:
            from ...interfaces.gui import WebInterface, ElectronApp
            self.web_interface = None
            self.electron_app = None
            self.WebInterface = WebInterface
            self.ElectronApp = ElectronApp
        except ImportError:
            logger.warning("GUI integration modules not available")
            self.WebInterface = None
            self.ElectronApp = None

    @property
    def name(self) -> str:
        return "gui"

    def can_handle(self, task: Task) -> bool:
        """Check if this is a GUI task"""
        return task.command.startswith("gui.")

    async def execute(self, task: Task) -> TaskResult:
        """Execute a GUI task"""
        try:
            if not self.WebInterface:
                return TaskResult(
                    success=False,
                    error="GUI interface not available"
                )

            command_parts = task.command.split(".")
            if len(command_parts) < 2:
                return TaskResult(
                    success=False,
                    error="Invalid GUI command format"
                )

            operation = command_parts[1]

            if operation == "start_web":
                return await self._start_web_interface(task)
            elif operation == "stop_web":
                return await self._stop_web_interface(task)
            elif operation == "start_electron":
                return await self._start_electron_app(task)
            elif operation == "stop_electron":
                return await self._stop_electron_app(task)
            elif operation == "status":
                return await self._get_gui_status(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown GUI operation: {operation}"
                )

        except Exception as e:
            logger.error(f"GUI task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )

    async def _start_web_interface(self, task: Task) -> TaskResult:
        """Start web interface"""
        if not self.WebInterface:
            return TaskResult(
                success=False,
                error="Web interface not available"
            )

        if not self.web_interface:
            self.web_interface = self.WebInterface()

        result = await self.web_interface.start()

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _stop_web_interface(self, task: Task) -> TaskResult:
        """Stop web interface"""
        if not self.web_interface:
            return TaskResult(success=False, error="Web interface not running")

        result = await self.web_interface.stop()
        self.web_interface = None

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _start_electron_app(self, task: Task) -> TaskResult:
        """Start Electron app"""
        if not self.ElectronApp:
            return TaskResult(success=False, error="Electron app not available")

        if not self.electron_app:
            self.electron_app = self.ElectronApp()

        result = await self.electron_app.start()

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _stop_electron_app(self, task: Task) -> TaskResult:
        """Stop Electron app"""
        if not self.electron_app:
            return TaskResult(success=False, error="Electron app not running")

        result = await self.electron_app.stop()
        self.electron_app = None

        return TaskResult(
            success=result["success"],
            data=result if result["success"] else None,
            error=result.get("error")
        )

    async def _get_gui_status(self, task: Task) -> TaskResult:
        """Get GUI status"""
        status = {
            "web_interface": {
                "available": self.WebInterface is not None,
                "running": self.web_interface is not None,
                "status": self.web_interface.get_status() if self.web_interface else None
            },
            "electron_app": {
                "available": self.ElectronApp is not None,
                "running": self.electron_app is not None,
                "status": self.electron_app.get_status() if self.electron_app else None
            }
        }

        return TaskResult(
            success=True,
            data=status
        )
