"""
Text-to-Speech for JARVIS AI Assistant
"""

import io
import logging
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile
import requests

from ...config import config

logger = logging.getLogger(__name__)


class TextToSpeech:
    """Multi-provider text-to-speech system"""
    
    def __init__(self):
        """Initialize text-to-speech"""
        self.provider = config.get("voice.tts.provider", "elevenlabs")
        self.voice_id = config.get("voice.tts.voice_id", "default")
        self.language = config.get("voice.tts.language", "en-US")
        self.speed = config.get("voice.tts.speed", 1.0)
        self.pitch = config.get("voice.tts.pitch", 0.0)
        
        # Initialize providers
        self.elevenlabs_api_key = config.get_api_key("elevenlabs")
        self.azure_speech_key = config.get_api_key("azure_speech")
        self.azure_region = config.get("voice.tts.azure_region", "eastus")
        
        self.pyttsx3_engine = None
        self.gtts_available = False
        
        self._init_providers()
        
        logger.info(f"Text-to-speech initialized with provider: {self.provider}")
    
    def _init_providers(self):
        """Initialize available TTS providers"""
        # Initialize pyttsx3 (local TTS)
        self.pyttsx3_engine = None
        try:
            import pyttsx3
            self.pyttsx3_engine = pyttsx3.init()

            # Configure voice settings
            voices = self.pyttsx3_engine.getProperty('voices')
            if voices and hasattr(voices, '__iter__'):
                # Try to find a suitable voice
                for voice in voices: # type: ignore
                    if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                        self.pyttsx3_engine.setProperty('voice', voice.id)
                        break

            self.pyttsx3_engine.setProperty('rate', int(200 * self.speed))
            logger.info("pyttsx3 TTS engine initialized")

        except ImportError:
            logger.warning("pyttsx3 not available. Install with: pip install pyttsx3")
        except Exception as e:
            logger.error(f"Failed to initialize pyttsx3: {e}")

        # Check gTTS availability
        self.gtts_available = False
        try:
            import gtts
            self.gtts_available = True
            logger.info("Google TTS (gTTS) available")
        except ImportError:
            logger.warning("gTTS not available. Install with: pip install gtts")
    
    async def speak(self, text: str, save_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert text to speech and optionally save to file
        
        Args:
            text: Text to convert to speech
            save_file: Optional path to save audio file
            
        Returns:
            Dictionary with operation result
        """
        try:
            if not text.strip():
                return {
                    "success": False,
                    "error": "No text provided"
                }
            
            if self.provider == "elevenlabs" and self.elevenlabs_api_key:
                return await self._speak_elevenlabs(text, save_file)
            elif self.provider == "azure" and self.azure_speech_key:
                return await self._speak_azure(text, save_file)
            elif self.provider == "gtts" and self.gtts_available:
                return await self._speak_gtts(text, save_file)
            elif self.provider == "pyttsx3" and self.pyttsx3_engine:
                return await self._speak_pyttsx3(text, save_file)
            else:
                return {
                    "success": False,
                    "error": f"Provider {self.provider} not available"
                }
                
        except Exception as e:
            logger.error(f"Error in text-to-speech: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _speak_elevenlabs(self, text: str, save_file: Optional[str] = None) -> Dict[str, Any]:
        """Generate speech using ElevenLabs API"""
        try:
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{self.voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.elevenlabs_api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5,
                    "style": 0.0,
                    "use_speaker_boost": True
                }
            }
            
            # Make API request
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(url, json=data, headers=headers, timeout=30)
            )
            
            if response.status_code == 200:
                audio_data = response.content
                
                # Save to file if requested
                if save_file:
                    with open(save_file, 'wb') as f:
                        f.write(audio_data)
                
                # Play audio
                await self._play_audio_data(audio_data)
                
                return {
                    "success": True,
                    "provider": "elevenlabs",
                    "audio_size": len(audio_data),
                    "saved_file": save_file
                }
            else:
                return {
                    "success": False,
                    "error": f"ElevenLabs API error: {response.status_code}",
                    "provider": "elevenlabs"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "elevenlabs"
            }
    
    async def _speak_azure(self, text: str, save_file: Optional[str] = None) -> Dict[str, Any]:
        """Generate speech using Azure Speech Services"""
        try:
            import azure.cognitiveservices.speech as speechsdk
            
            # Create speech config
            speech_config = speechsdk.SpeechConfig(
                subscription=self.azure_speech_key,
                region=self.azure_region
            )
            speech_config.speech_synthesis_voice_name = self.voice_id
            
            # Create synthesizer
            if save_file:
                audio_config = speechsdk.audio.AudioOutputConfig(filename=save_file)
            else:
                audio_config = speechsdk.audio.AudioOutputConfig(use_default_speaker=True)
            
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=speech_config,
                audio_config=audio_config
            )
            
            # Synthesize speech
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                synthesizer.speak_text,
                text
            )
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return {
                    "success": True,
                    "provider": "azure",
                    "saved_file": save_file
                }
            else:
                return {
                    "success": False,
                    "error": f"Azure synthesis failed: {result.reason}",
                    "provider": "azure"
                }
                
        except ImportError:
            return {
                "success": False,
                "error": "Azure Speech SDK not available",
                "provider": "azure"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "azure"
            }
    
    async def _speak_gtts(self, text: str, save_file: Optional[str] = None) -> Dict[str, Any]:
        """Generate speech using Google Text-to-Speech"""
        try:
            if not self.gtts_available:
                return {
                    "success": False,
                    "error": "gTTS not available",
                    "provider": "gtts"
                }

            from gtts import gTTS
            import pygame

            # Create gTTS object
            tts = gTTS(text=text, lang=self.language[:2], slow=False)
            
            # Save to file or temporary file
            if save_file:
                output_file = save_file
            else:
                temp_file = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
                output_file = temp_file.name
                temp_file.close()
            
            # Generate speech
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, tts.save, output_file)
            
            # Play audio if not saving to specific file
            if not save_file:
                await self._play_audio_file(output_file)
                Path(output_file).unlink()  # Clean up temp file
            
            return {
                "success": True,
                "provider": "gtts",
                "saved_file": save_file
            }
            
        except ImportError:
            return {
                "success": False,
                "error": "gTTS not available",
                "provider": "gtts"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "gtts"
            }
    
    async def _speak_pyttsx3(self, text: str, save_file: Optional[str] = None) -> Dict[str, Any]:
        """Generate speech using pyttsx3 (local TTS)"""
        try:
            if self.pyttsx3_engine is None:
                return {
                    "success": False,
                    "error": "pyttsx3 engine not initialized",
                    "provider": "pyttsx3"
                }

            if save_file:
                # pyttsx3 doesn't support direct file saving, so we'll speak and record
                # For now, just speak directly as file saving is complex with pyttsx3
                logger.warning("pyttsx3 doesn't support direct file saving, speaking instead")
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self.pyttsx3_engine.say, text)
                await loop.run_in_executor(None, self.pyttsx3_engine.runAndWait)
            else:
                # Speak directly
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self.pyttsx3_engine.say, text)
                await loop.run_in_executor(None, self.pyttsx3_engine.runAndWait)
            
            return {
                "success": True,
                "provider": "pyttsx3",
                "saved_file": save_file
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "pyttsx3"
            }
    
    async def _play_audio_data(self, audio_data: bytes) -> None:
        """Play audio data"""
        try:
            import pygame
            
            # Initialize pygame mixer
            pygame.mixer.init()
            
            # Create audio stream
            audio_stream = io.BytesIO(audio_data)
            
            # Load and play
            pygame.mixer.music.load(audio_stream)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)
                
        except ImportError:
            logger.warning("pygame not available for audio playback")
        except Exception as e:
            logger.error(f"Error playing audio: {e}")
    
    async def _play_audio_file(self, file_path: str) -> None:
        """Play audio file"""
        try:
            import pygame
            
            # Initialize pygame mixer
            pygame.mixer.init()
            
            # Load and play
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)
                
        except ImportError:
            logger.warning("pygame not available for audio playback")
        except Exception as e:
            logger.error(f"Error playing audio file: {e}")
    
    async def get_voices(self) -> Dict[str, Any]:
        """Get available voices for current provider"""
        try:
            if self.provider == "elevenlabs" and self.elevenlabs_api_key:
                return await self._get_elevenlabs_voices()
            elif self.provider == "azure" and self.azure_speech_key:
                return await self._get_azure_voices()
            elif self.provider == "pyttsx3" and self.pyttsx3_engine:
                return await self._get_pyttsx3_voices()
            else:
                return {
                    "success": False,
                    "error": f"Voice listing not supported for {self.provider}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_elevenlabs_voices(self) -> Dict[str, Any]:
        """Get ElevenLabs voices"""
        try:
            url = "https://api.elevenlabs.io/v1/voices"
            headers = {"xi-api-key": self.elevenlabs_api_key}
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(url, headers=headers, timeout=10)
            )
            
            if response.status_code == 200:
                voices_data = response.json()
                voices = []
                
                for voice in voices_data.get("voices", []):
                    voices.append({
                        "id": voice["voice_id"],
                        "name": voice["name"],
                        "category": voice.get("category", "unknown"),
                        "description": voice.get("description", "")
                    })
                
                return {
                    "success": True,
                    "voices": voices,
                    "provider": "elevenlabs"
                }
            else:
                return {
                    "success": False,
                    "error": f"API error: {response.status_code}",
                    "provider": "elevenlabs"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "elevenlabs"
            }
    
    async def _get_azure_voices(self) -> Dict[str, Any]:
        """Get Azure voices"""
        try:
            import azure.cognitiveservices.speech as speechsdk
            
            speech_config = speechsdk.SpeechConfig(
                subscription=self.azure_speech_key,
                region=self.azure_region
            )
            
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config)
            
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: synthesizer.get_voices_async().get()
            )

            voices = []
            if result is not None and hasattr(result, 'voices') and result.voices:
                for voice in result.voices:
                    voices.append({
                        "id": getattr(voice, 'short_name', 'unknown'),
                        "name": getattr(voice, 'local_name', 'unknown'),
                        "gender": getattr(voice.gender, 'name', 'unknown') if hasattr(voice, 'gender') else 'unknown',
                        "locale": getattr(voice, 'locale', 'unknown')
                    })
            
            return {
                "success": True,
                "voices": voices,
                "provider": "azure"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "azure"
            }
    
    async def _get_pyttsx3_voices(self) -> Dict[str, Any]:
        """Get pyttsx3 voices"""
        try:
            if self.pyttsx3_engine is None:
                return {
                    "success": False,
                    "error": "pyttsx3 engine not initialized",
                    "provider": "pyttsx3"
                }

            voices = self.pyttsx3_engine.getProperty('voices')
            voice_list = []

            if voices and hasattr(voices, '__iter__'):
                for voice in voices: # type: ignore
                    voice_list.append({
                        "id": voice.id,
                        "name": voice.name,
                        "age": getattr(voice, 'age', 'unknown'),
                        "gender": getattr(voice, 'gender', 'unknown')
                    })

            return {
                "success": True,
                "voices": voice_list,
                "provider": "pyttsx3"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": "pyttsx3"
            }
    
    def set_voice(self, voice_id: str) -> bool:
        """Set the voice to use"""
        try:
            self.voice_id = voice_id
            
            if self.provider == "pyttsx3" and self.pyttsx3_engine:
                self.pyttsx3_engine.setProperty('voice', voice_id)
            
            return True
        except Exception as e:
            logger.error(f"Error setting voice: {e}")
            return False
    
    def get_available_providers(self) -> List[str]:
        """Get list of available TTS providers"""
        providers = []
        
        if self.elevenlabs_api_key:
            providers.append("elevenlabs")
        if self.azure_speech_key:
            providers.append("azure")
        if self.gtts_available:
            providers.append("gtts")
        if self.pyttsx3_engine:
            providers.append("pyttsx3")
            
        return providers
    
    def get_stats(self) -> Dict[str, Any]:
        """Get TTS statistics"""
        return {
            "provider": self.provider,
            "voice_id": self.voice_id,
            "language": self.language,
            "speed": self.speed,
            "pitch": self.pitch,
            "available_providers": self.get_available_providers(),
            "elevenlabs_available": bool(self.elevenlabs_api_key),
            "azure_available": bool(self.azure_speech_key),
            "gtts_available": self.gtts_available,
            "pyttsx3_available": self.pyttsx3_engine is not None
        }
