#!/usr/bin/env python3
"""
Test script for JARVIS Text-to-Speech functionality
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from jarvis.integrations.voice.text_to_speech import TextToSpeech


async def test_tts():
    """Test TTS functionality"""
    print("Testing JARVIS Text-to-Speech...")
    
    # Initialize TTS
    tts = TextToSpeech()
    
    # Get available providers
    providers = tts.get_available_providers()
    print(f"Available TTS providers: {providers}")
    
    if not providers:
        print("No TTS providers available. Please install dependencies:")
        print("pip install -r requirements-tts.txt")
        return
    
    # Test with the first available provider
    test_text = "Hello, this is JARVIS testing text-to-speech functionality."
    
    for provider in providers:
        print(f"\nTesting {provider} provider...")
        tts.provider = provider
        
        try:
            result = await tts.speak(test_text)
            if result["success"]:
                print(f"✓ {provider} TTS successful")
            else:
                print(f"✗ {provider} TTS failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"✗ {provider} TTS error: {e}")
    
    # Test voice listing
    print(f"\nTesting voice listing for {tts.provider}...")
    try:
        voices_result = await tts.get_voices()
        if voices_result["success"]:
            voices = voices_result["voices"]
            print(f"Found {len(voices)} voices")
            for voice in voices[:3]:  # Show first 3 voices
                print(f"  - {voice.get('name', voice.get('id'))}")
        else:
            print(f"Voice listing failed: {voices_result.get('error')}")
    except Exception as e:
        print(f"Voice listing error: {e}")
    
    # Show TTS stats
    print(f"\nTTS Statistics:")
    stats = tts.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    asyncio.run(test_tts())
