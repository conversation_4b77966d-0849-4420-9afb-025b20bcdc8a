"""
Entity Extraction for JARVIS AI Assistant
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class Entity:
    """Extracted entity"""
    type: str
    value: str
    confidence: float
    start_pos: int = -1
    end_pos: int = -1
    normalized_value: Any = None


class EntityExtractor:
    """Extracts entities from natural language text"""
    
    def __init__(self):
        """Initialize the entity extractor"""
        self.extraction_patterns = self._load_extraction_patterns()
        self.extraction_stats = {
            "total_extractions": 0,
            "entities_found": 0,
            "entity_type_counts": {}
        }
        
        logger.info("Entity extractor initialized")
    
    def _load_extraction_patterns(self) -> Dict[str, List[Tuple[str, str]]]:
        """Load entity extraction patterns"""
        return {
            "number": [
                (r'\b(\d+(?:\.\d+)?)\b', "float"),
                (r'\b(zero|one|two|three|four|five|six|seven|eight|nine|ten)\b', "word_number"),
                (r'\b(eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty)\b', "word_number"),
                (r'\b(thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred|thousand|million|billion)\b', "word_number")
            ],
            
            "time": [
                (r'\b(\d{1,2}:\d{2}(?::\d{2})?(?:\s*[ap]m)?)\b', "time_format"),
                (r'\b(\d{1,2}\s*[ap]m)\b', "time_12hour"),
                (r'\b(morning|afternoon|evening|night|noon|midnight)\b', "time_period"),
                (r'\b(now|today|tomorrow|yesterday)\b', "relative_time"),
                (r'\b(in\s+\d+\s+(?:minutes?|hours?|days?))\b', "time_offset")
            ],
            
            "date": [
                (r'\b(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\b', "date_format"),
                (r'\b(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})\b', "iso_date"),
                (r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b', "month_name"),
                (r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b', "day_name"),
                (r'\b(today|tomorrow|yesterday|next week|last week|next month|last month)\b', "relative_date")
            ],
            
            "email": [
                (r'\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b', "email_address")
            ],
            
            "phone": [
                (r'\b(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})\b', "phone_number"),
                (r'\b(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})\b', "phone_simple")
            ],
            
            "url": [
                (r'\b(https?://[^\s]+)\b', "web_url"),
                (r'\b(www\.[^\s]+\.[a-zA-Z]{2,})\b', "www_url")
            ],
            
            "file_path": [
                (r'\b([A-Za-z]:\\[^<>:"|?*\n\r]+)\b', "windows_path"),
                (r'\b(\/[^<>:"|?*\n\r\s]+)\b', "unix_path"),
                (r'\b([^\s]+\.[a-zA-Z0-9]{1,4})\b', "filename")
            ],
            
            "currency": [
                (r'\$(\d+(?:\.\d{2})?)\b', "dollar_amount"),
                (r'\b(\d+(?:\.\d{2})?\s*(?:dollars?|cents?|usd))\b', "currency_word"),
                (r'€(\d+(?:\.\d{2})?)\b', "euro_amount"),
                (r'£(\d+(?:\.\d{2})?)\b', "pound_amount")
            ],
            
            "percentage": [
                (r'\b(\d+(?:\.\d+)?%)\b', "percentage"),
                (r'\b(\d+(?:\.\d+)?\s*percent)\b', "percent_word")
            ],
            
            "location": [
                (r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*,\s*[A-Z]{2})\b', "city_state"),
                (r'\b(\d{5}(?:-\d{4})?)\b', "zip_code"),
                (r'\b(here|there|home|work|office)\b', "relative_location")
            ],
            
            "person": [
                (r'\b([A-Z][a-z]+\s+[A-Z][a-z]+)\b', "full_name"),
                (r'\b(mr|mrs|ms|dr|prof)\.\s*([A-Z][a-z]+)\b', "titled_name")
            ],
            
            "operation": [
                (r'\b(create|make|build|generate|add|insert)\b', "create_operation"),
                (r'\b(delete|remove|erase|clear|destroy)\b', "delete_operation"),
                (r'\b(update|modify|change|edit|alter)\b', "update_operation"),
                (r'\b(copy|duplicate|clone)\b', "copy_operation"),
                (r'\b(move|transfer|relocate)\b', "move_operation"),
                (r'\b(open|launch|start|run|execute)\b', "open_operation"),
                (r'\b(close|quit|exit|stop|terminate)\b', "close_operation")
            ],
            
            "quantity": [
                (r'\b(\d+\s*(?:gb|mb|kb|tb|bytes?))\b', "data_size"),
                (r'\b(\d+\s*(?:kg|g|lb|oz|pounds?))\b', "weight"),
                (r'\b(\d+\s*(?:km|m|cm|mm|miles?|feet|ft|inches?|in))\b', "distance"),
                (r'\b(\d+\s*(?:seconds?|minutes?|hours?|days?|weeks?|months?|years?))\b', "duration")
            ]
        }
    
    def extract(self, text: str, intent: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract entities from text
        
        Args:
            text: Input text
            intent: Optional intent to focus extraction
            
        Returns:
            Dictionary of extracted entities
        """
        self.extraction_stats["total_extractions"] += 1
        
        entities = {}
        all_entities = []
        
        # Extract entities based on patterns
        for entity_type, patterns in self.extraction_patterns.items():
            for pattern, subtype in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    entity = Entity(
                        type=entity_type,
                        value=match.group(1) if match.groups() else match.group(0),
                        confidence=self._calculate_confidence(entity_type, match.group(0), intent),
                        start_pos=match.start(),
                        end_pos=match.end(),
                        normalized_value=self._normalize_entity_value(entity_type, match.group(0))
                    )
                    all_entities.append(entity)
        
        # Group entities by type and select best matches
        for entity in all_entities:
            if entity.confidence > 0.5:  # Confidence threshold
                if entity.type not in entities:
                    entities[entity.type] = []
                entities[entity.type].append({
                    "value": entity.value,
                    "normalized": entity.normalized_value,
                    "confidence": entity.confidence,
                    "position": (entity.start_pos, entity.end_pos)
                })
        
        # Update stats
        if entities:
            self.extraction_stats["entities_found"] += len(entities)
            for entity_type in entities:
                self.extraction_stats["entity_type_counts"][entity_type] = \
                    self.extraction_stats["entity_type_counts"].get(entity_type, 0) + 1
        
        # Intent-specific post-processing
        if intent:
            entities = self._post_process_for_intent(entities, intent, text)
        
        logger.debug(f"Extracted entities: {list(entities.keys())}")
        return entities
    
    def _calculate_confidence(self, entity_type: str, value: str, intent: Optional[str]) -> float:
        """Calculate confidence score for an entity"""
        base_confidence = 0.7
        
        # Intent-based confidence boost
        intent_boosts = {
            "calculation": {"number": 0.2, "operation": 0.1},
            "get_time": {"time": 0.3},
            "get_date": {"date": 0.3},
            "send_email": {"email": 0.3, "person": 0.1},
            "file_operation": {"file_path": 0.2, "operation": 0.2},
            "search": {"url": 0.1},
            "set_reminder": {"time": 0.2, "date": 0.2}
        }
        
        if intent and intent in intent_boosts and entity_type in intent_boosts[intent]:
            base_confidence += intent_boosts[intent][entity_type]
        
        # Value-based confidence adjustments
        if entity_type == "email" and "@" in value and "." in value:
            base_confidence += 0.2
        elif entity_type == "phone" and len(re.sub(r'[^\d]', '', value)) >= 10:
            base_confidence += 0.2
        elif entity_type == "url" and value.startswith(("http://", "https://", "www.")):
            base_confidence += 0.2
        
        return min(base_confidence, 1.0)
    
    def _normalize_entity_value(self, entity_type: str, value: str) -> Any:
        """Normalize entity value to standard format"""
        try:
            if entity_type == "number":
                # Convert word numbers to digits
                word_to_num = {
                    "zero": 0, "one": 1, "two": 2, "three": 3, "four": 4,
                    "five": 5, "six": 6, "seven": 7, "eight": 8, "nine": 9,
                    "ten": 10, "eleven": 11, "twelve": 12, "thirteen": 13,
                    "fourteen": 14, "fifteen": 15, "sixteen": 16, "seventeen": 17,
                    "eighteen": 18, "nineteen": 19, "twenty": 20, "thirty": 30,
                    "forty": 40, "fifty": 50, "sixty": 60, "seventy": 70,
                    "eighty": 80, "ninety": 90, "hundred": 100, "thousand": 1000
                }
                
                if value.lower() in word_to_num:
                    return word_to_num[value.lower()]
                else:
                    return float(value) if '.' in value else int(value)
            
            elif entity_type == "time":
                # Normalize time formats
                if ":" in value:
                    return value  # Keep time format as is
                elif value.lower() in ["morning", "afternoon", "evening", "night"]:
                    return value.lower()
                else:
                    return value
            
            elif entity_type == "date":
                # Normalize date formats
                return value  # Keep as string for now, can be enhanced with datetime parsing
            
            elif entity_type == "email":
                return value.lower()
            
            elif entity_type == "phone":
                # Remove formatting from phone numbers
                return re.sub(r'[^\d]', '', value)
            
            elif entity_type == "currency":
                # Extract numeric value
                numeric = re.search(r'(\d+(?:\.\d{2})?)', value)
                return float(numeric.group(1)) if numeric else 0.0
            
            elif entity_type == "percentage":
                # Extract numeric value
                numeric = re.search(r'(\d+(?:\.\d+)?)', value)
                return float(numeric.group(1)) if numeric else 0.0
            
            else:
                return value
                
        except (ValueError, AttributeError):
            return value
    
    def _post_process_for_intent(self, entities: Dict[str, Any], intent: str, text: str) -> Dict[str, Any]:
        """Post-process entities based on intent"""
        if intent == "calculation":
            # Extract mathematical operations
            operations = re.findall(r'[\+\-\*\/\=]', text)
            if operations:
                entities["math_operations"] = [{"value": op, "normalized": op, "confidence": 0.9} for op in operations]
        
        elif intent == "file_operation":
            # Extract operation type
            if "operation" in entities:
                op_type = entities["operation"][0]["value"].lower()
                entities["operation_type"] = [{"value": op_type, "normalized": op_type, "confidence": 0.8}]
        
        elif intent == "search":
            # Extract search query (everything except common words)
            search_terms = self._extract_search_terms(text)
            if search_terms:
                entities["search_query"] = [{"value": " ".join(search_terms), "normalized": search_terms, "confidence": 0.7}]
        
        return entities
    
    def _extract_search_terms(self, text: str) -> List[str]:
        """Extract search terms from text"""
        # Remove common search words
        stop_words = {"search", "for", "find", "look", "google", "bing", "information", "about"}
        words = text.lower().split()
        search_terms = [word for word in words if word not in stop_words and len(word) > 2]
        return search_terms
    
    def get_stats(self) -> Dict[str, Any]:
        """Get extraction statistics"""
        avg_entities = 0.0
        if self.extraction_stats["total_extractions"] > 0:
            avg_entities = self.extraction_stats["entities_found"] / self.extraction_stats["total_extractions"]
        
        return {
            "total_extractions": self.extraction_stats["total_extractions"],
            "entities_found": self.extraction_stats["entities_found"],
            "average_entities_per_text": avg_entities,
            "supported_entity_types": len(self.extraction_patterns),
            "entity_type_distribution": self.extraction_stats["entity_type_counts"]
        }
