"""
Logging utilities for JARVIS AI Assistant
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional

from ..config import config


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    console_output: bool = True
):
    """
    Set up logging configuration for JARVIS
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        console_output: Whether to output to console
    """
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Get configuration
    if not log_file:
        log_file = config.get("logging.file_path", "logs/jarvis.log")
    
    log_format = config.get(
        "logging.format", 
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    max_file_size = config.get("logging.max_file_size", "10MB")
    backup_count = config.get("logging.backup_count", 5)
    
    # Convert max_file_size to bytes
    if isinstance(max_file_size, str):
        if max_file_size.endswith("MB"):
            max_bytes = int(max_file_size[:-2]) * 1024 * 1024
        elif max_file_size.endswith("KB"):
            max_bytes = int(max_file_size[:-2]) * 1024
        else:
            max_bytes = int(max_file_size)
    else:
        max_bytes = max_file_size
    
    # Set up root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(log_format)
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, level.upper()))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Set specific logger levels
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("anthropic").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Log the setup
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured - Level: {level}, File: {log_file}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)
